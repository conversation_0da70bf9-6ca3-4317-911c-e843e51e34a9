// Theme management
function toggleTheme() {
    const isDark = document.documentElement.classList.toggle('dark');
    localStorage.setItem('theme', isDark ? 'dark' : 'light');
    
    // Update user preferences in database
    updateUserPreferences('theme', isDark ? 'dark' : 'light');
}

// Load saved theme on page load
function loadTheme() {
    const savedTheme = localStorage.getItem('theme');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
        document.documentElement.classList.add('dark');
    }
}

// Update user preferences
async function updateUserPreferences(key, value) {
    try {
        const response = await fetch('api/update-preferences.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ [key]: value })
        });
        
        if (!response.ok) {
            console.error('Failed to update preferences');
        }
    } catch (error) {
        console.error('Error updating preferences:', error);
    }
}

// Initialize theme on page load
document.addEventListener('DOMContentLoaded', loadTheme);
<?php
// Set 404 status code
http_response_code(404);

// Get language preference from URL or default to Arabic
$language = $_GET['lang'] ?? 'ar';
if (!in_array($language, ['ar', 'en', 'fr'])) {
    $language = 'ar';
}

// Include language file
require_once "lang/{$language}.php";

// Get theme from cookie or default to light
$theme = $_COOKIE['theme'] ?? 'light';
?>

<!DOCTYPE html>
<html lang="<?php echo $language; ?>" dir="<?php echo $language === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $lang['pageNotFound'] ?? 'Page Not Found'; ?> - <?php echo $lang['appTitle']; ?></title>
    <meta name="description" content="<?php echo $lang['pageNotFoundDesc'] ?? 'The page you are looking for could not be found.'; ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a'
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="<?php echo $theme === 'dark' ? 'dark' : ''; ?> bg-gray-50 dark:bg-gray-900 min-h-screen flex items-center justify-center">
    <!-- Language Selector -->
    <div class="fixed top-4 <?php echo $language === 'ar' ? 'right-4' : 'left-4'; ?> z-50">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-2 border dark:border-gray-700">
            <div class="flex gap-2">
                <a href="?lang=ar" class="w-8 h-6 rounded overflow-hidden border-2 <?php echo $language === 'ar' ? 'border-primary-500' : 'border-transparent hover:border-gray-300'; ?>" title="العربية">
                    <img src="https://flagcdn.com/w40/sa.jpg" alt="العربية" class="w-full h-full object-cover">
                </a>
                <a href="?lang=en" class="w-8 h-6 rounded overflow-hidden border-2 <?php echo $language === 'en' ? 'border-primary-500' : 'border-transparent hover:border-gray-300'; ?>" title="English">
                    <img src="https://flagcdn.com/w40/us.jpg" alt="English" class="w-full h-full object-cover">
                </a>
                <a href="?lang=fr" class="w-8 h-6 rounded overflow-hidden border-2 <?php echo $language === 'fr' ? 'border-primary-500' : 'border-transparent hover:border-gray-300'; ?>" title="Français">
                    <img src="https://flagcdn.com/w40/fr.jpg" alt="Français" class="w-full h-full object-cover">
                </a>
            </div>
        </div>
    </div>

    <!-- Theme Toggle -->
    <div class="fixed top-4 <?php echo $language === 'ar' ? 'left-4' : 'right-4'; ?> z-50">
        <button onclick="toggleTheme()" class="bg-white dark:bg-gray-800 p-3 rounded-full shadow-lg border dark:border-gray-700 hover:shadow-xl transition-all duration-300">
            <svg class="h-5 w-5 text-gray-700 dark:hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
            </svg>
            <svg class="h-5 w-5 text-yellow-400 hidden dark:block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
            </svg>
        </button>
    </div>

    <!-- Main Content -->
    <div class="text-center px-4 max-w-lg mx-auto">
        <!-- 404 Illustration -->
        <div class="mb-8">
            <div class="relative">
                <div class="text-9xl font-bold text-primary-500 opacity-20 select-none">404</div>
                <div class="absolute inset-0 flex items-center justify-center">
                    <svg class="w-24 h-24 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 20.5a7.962 7.962 0 01-5-1.709M15 3.5a7.966 7.966 0 00-6 0M9 3.5a7.966 7.966 0 006 0"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Error Message -->
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            <?php echo $lang['pageNotFound'] ?? 'Page Not Found'; ?>
        </h1>
        
        <p class="text-lg text-gray-600 dark:text-gray-400 mb-8">
            <?php echo $lang['pageNotFoundMessage'] ?? 'Sorry, the page you are looking for could not be found. It might have been moved, deleted, or you entered the wrong URL.'; ?>
        </p>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="javascript:history.back()" 
               class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <svg class="w-4 h-4 <?php echo $language === 'ar' ? 'ml-2' : 'mr-2'; ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                <?php echo $lang['goBack'] ?? 'Go Back'; ?>
            </a>
            
            <a href="index.php" 
               class="inline-flex items-center justify-center px-6 py-3 bg-primary-500 hover:bg-primary-600 text-white rounded-lg transition-colors">
                <svg class="w-4 h-4 <?php echo $language === 'ar' ? 'ml-2' : 'mr-2'; ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                <?php echo $lang['goHome'] ?? 'Go Home'; ?>
            </a>
        </div>

        <!-- Helpful Links -->
        <div class="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                <?php echo $lang['helpfulLinks'] ?? 'Helpful Links'; ?>:
            </p>
            <div class="flex flex-wrap justify-center gap-4 text-sm">
                <a href="index.php" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors">
                    <?php echo $lang['dashboard']; ?>
                </a>
                <a href="tasks.php" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors">
                    <?php echo $lang['tasks']; ?>
                </a>
                <a href="calendar.php" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors">
                    <?php echo $lang['calendar']; ?>
                </a>
                <a href="about.php" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors">
                    <?php echo $lang['about'] ?? 'About'; ?>
                </a>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Theme handling
        function toggleTheme() {
            document.documentElement.classList.toggle('dark');
            const isDark = document.documentElement.classList.contains('dark');
            document.cookie = `theme=${isDark ? 'dark' : 'light'}; path=/; max-age=31536000`;
        }

        // Load saved theme
        const savedTheme = document.cookie.split('; ').find(row => row.startsWith('theme='));
        if (savedTheme) {
            const theme = savedTheme.split('=')[1];
            if (theme === 'dark') {
                document.documentElement.classList.add('dark');
            }
        }
    </script>
</body>
</html>

# Task & Productivity Manager

## Overview

This is a full-stack web application built for task and productivity management with AI assistance. The system features a PHP backend with vanilla HTML/CSS/JavaScript frontend, PostgreSQL database, and session-based authentication. The application supports multiple languages (Arabic as default, English, French) with full RTL support and includes features for task management, calendar integration, AI assistance, and progress tracking.

## User Preferences

Preferred communication style: Simple, everyday language.
Technology stack: PHP backend with MySQL/PostgreSQL database, HTML/CSS with TailwindCSS, and vanilla JavaScript.

## System Architecture

### Frontend Architecture
- **Technology**: Vanilla HTML5, CSS3, and JavaScript (ES6+)
- **Styling**: TailwindCSS via CDN for rapid UI development
- **Internationalization**: PHP-based language system with Arabic, English, and French support
- **Theme System**: JavaScript-based dark/light mode with localStorage persistence
- **Layout**: Responsive design with full RTL support for Arabic language
- **Icons**: SVG icons and Lucide icons for consistent UI

### Backend Architecture
- **Language**: PHP 8.2 with PDO for database operations
- **Database**: SQLite with native PHP PDO connection for simplicity and portability
- **Authentication**: Session-based authentication with secure password hashing
- **Session Management**: PHP native sessions with file-based storage

### Key Components

#### Authentication System
- **Method**: Traditional username/password authentication with PHP sessions
- **Session Storage**: PHP native sessions with database persistence
- **User Management**: Registration and login with secure password hashing
- **Security**: Session-based authentication with CSRF protection

#### Database Schema
- **Users Table**: Stores user profiles (id, first_name, last_name, email, password_hash, profile_image_url, timestamps)
- **Tasks Table**: Task management with priority levels (low, medium, high) and status tracking (pending, in_progress, completed)
- **Quotes Table**: Motivational quotes with multi-language support (Arabic, English, French)
- **User Preferences**: Theme (light/dark) and language (ar/en/fr) preferences per user

#### API Structure (RESTful PHP endpoints)
- **Authentication**: `login.php`, `logout.php` for user authentication
- **Task Management**: `api/create-task.php`, `api/update-task.php`, `api/delete-task.php`, `api/get-task.php`
- **User Preferences**: `api/update-preferences.php` for theme and language settings
- **Statistics**: Built-in functions for task completion metrics and weekly progress

#### UI Components (PHP + HTML + TailwindCSS)
- **Dashboard**: Overview with today's tasks, statistics, and quick actions (`index.php`)
- **Task Management**: Modular components for CRUD operations with AJAX
- **Calendar Integration**: Mini calendar component with JavaScript functionality
- **Progress Tracking**: Weekly statistics with visual progress bars
- **AI Assistant**: Motivational quotes system with multi-language support

## Data Flow

1. **Authentication Flow**: User authenticates via Replit OIDC, session created in PostgreSQL
2. **Task Operations**: Frontend sends requests to Express API, which validates sessions and performs database operations via Drizzle ORM
3. **Real-time Updates**: TanStack Query handles caching and invalidation for responsive UI updates
4. **Internationalization**: Language changes trigger API calls to update user preferences and reload interface text
5. **Theme Management**: Theme changes persist to database and localStorage for consistent experience

## External Dependencies

### Core Dependencies
- **@neondatabase/serverless**: PostgreSQL serverless connection
- **drizzle-orm**: Type-safe database operations
- **@tanstack/react-query**: Server state management
- **react-i18next**: Internationalization
- **passport**: Authentication middleware
- **openid-client**: OIDC authentication

### UI Dependencies
- **@radix-ui/***: Accessible UI primitives
- **tailwindcss**: Utility-first CSS framework
- **lucide-react**: Icon library
- **class-variance-authority**: Component variant management

### Development Dependencies
- **vite**: Build tool and development server
- **typescript**: Type checking
- **@types/***: Type definitions

## Deployment Strategy

### Development Environment
- **Development Server**: Vite dev server with HMR
- **Backend**: Express server with tsx for TypeScript execution
- **Database**: Neon PostgreSQL with connection pooling
- **Environment Variables**: DATABASE_URL, SESSION_SECRET, REPL_ID, ISSUER_URL

### Production Build
- **Frontend**: Vite builds static assets to `dist/public`
- **Backend**: esbuild bundles Express server to `dist/index.js`
- **Database Migrations**: Drizzle Kit for schema migrations
- **Asset Serving**: Express serves static files in production

### Key Environment Configuration
- **SESSION_SECRET**: Required for session encryption
- **DATABASE_URL**: PostgreSQL connection string
- **REPL_ID**: Replit environment identifier
- **ISSUER_URL**: OIDC provider URL (defaults to Replit)

### Deployment Process
1. `npm run build`: Builds both frontend and backend
2. `npm run db:push`: Applies database schema changes
3. `npm start`: Runs production server
4. Static assets served from `/dist/public`
5. API routes handled by Express backend

The application is designed for Replit deployment with automatic database provisioning and authentication integration.
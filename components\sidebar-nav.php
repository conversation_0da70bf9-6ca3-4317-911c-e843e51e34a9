<aside class="fixed top-0 <?php echo $language === 'ar' ? 'right-0' : 'left-0'; ?> z-40 w-64 h-screen bg-white dark:bg-gray-800 border-<?php echo $language === 'ar' ? 'l' : 'r'; ?> dark:border-gray-700 shadow-lg">
    <div class="h-full px-4 py-6">
        <!-- Logo -->
        <div class="flex items-center mb-8">
            <div class="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
            </div>
            <span class="<?php echo $language === 'ar' ? 'mr-3' : 'ml-3'; ?> text-lg font-semibold text-gray-900 dark:text-white">
                <?php echo $lang['appTitle']; ?>
            </span>
        </div>

        <!-- Navigation -->
        <nav class="space-y-2">
            <a href="index.php" class="flex items-center px-3 py-2 text-gray-700 dark:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h2a2 2 0 012 2v6a2 2 0 01-2 2H10a2 2 0 01-2-2V5z"></path>
                </svg>
                <span class="<?php echo $language === 'ar' ? 'mr-3' : 'ml-3'; ?>"><?php echo $lang['dashboard']; ?></span>
            </a>

            <a href="tasks.php" class="flex items-center px-3 py-2 text-gray-700 dark:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                <span class="<?php echo $language === 'ar' ? 'mr-3' : 'ml-3'; ?>"><?php echo $lang['tasks']; ?></span>
            </a>

            <a href="calendar.php" class="flex items-center px-3 py-2 text-gray-700 dark:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <span class="<?php echo $language === 'ar' ? 'mr-3' : 'ml-3'; ?>"><?php echo $lang['calendar']; ?></span>
            </a>

            <a href="ai-assistant.php" class="flex items-center px-3 py-2 text-gray-700 dark:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364-.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
                <span class="<?php echo $language === 'ar' ? 'mr-3' : 'ml-3'; ?>"><?php echo $lang['aiAssistant']; ?></span>
            </a>

            <a href="progress.php" class="flex items-center px-3 py-2 text-gray-700 dark:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
                <span class="<?php echo $language === 'ar' ? 'mr-3' : 'ml-3'; ?>"><?php echo $lang['progress']; ?></span>
            </a>
        </nav>

        <!-- User Profile & Settings -->
        <div class="absolute bottom-4 left-4 right-4">
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-medium">
                                <?php echo strtoupper(substr($user['first_name'] ?: $user['email'], 0, 1)); ?>
                            </span>
                        </div>
                        <span class="<?php echo $language === 'ar' ? 'mr-2' : 'ml-2'; ?> text-sm text-gray-700 dark:text-gray-300">
                            <?php echo $user['first_name'] ?: explode('@', $user['email'])[0]; ?>
                        </span>
                    </div>
                    <a href="logout.php" class="text-gray-400 hover:text-red-500 transition-colors" title="<?php echo $lang['logout']; ?>">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>
</aside>
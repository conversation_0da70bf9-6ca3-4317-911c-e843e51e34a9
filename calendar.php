<?php
session_start();
require_once 'config/db.php';
require_once 'includes/auth.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// Get user data and preferences
$user = getUserById($_SESSION['user_id']);
$preferences = getUserPreferences($_SESSION['user_id']);
$theme = $preferences['theme'] ?? 'light';
$language = $preferences['language'] ?? 'ar';

// Include language file
require_once "lang/{$language}.php";

// Get current month and year
$current_month = isset($_GET['month']) ? (int)$_GET['month'] : date('n');
$current_year = isset($_GET['year']) ? (int)$_GET['year'] : date('Y');

// Validate month and year
$current_month = max(1, min(12, $current_month));
$current_year = max(2020, min(2030, $current_year));

// Get tasks for the current month
try {
    $pdo = getDB();
    $start_date = "$current_year-$current_month-01";
    $end_date = date('Y-m-t', strtotime($start_date));
    
    $stmt = $pdo->prepare("
        SELECT DATE(due_date) as date, COUNT(*) as count,
               COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
               GROUP_CONCAT(CONCAT(id, ':', title, ':', priority, ':', status) SEPARATOR '|') as tasks_data
        FROM tasks 
        WHERE user_id = ? AND DATE(due_date) BETWEEN ? AND ?
        GROUP BY DATE(due_date)
    ");
    $stmt->execute([$_SESSION['user_id'], $start_date, $end_date]);
    $calendar_data = [];
    while ($row = $stmt->fetch()) {
        $calendar_data[$row['date']] = $row;
    }
    
} catch (PDOException $e) {
    error_log("Calendar query error: " . $e->getMessage());
    $calendar_data = [];
}

// Calendar navigation
$prev_month = $current_month - 1;
$prev_year = $current_year;
if ($prev_month < 1) {
    $prev_month = 12;
    $prev_year--;
}

$next_month = $current_month + 1;
$next_year = $current_year;
if ($next_month > 12) {
    $next_month = 1;
    $next_year++;
}

$month_names = [
    1 => $lang['january'], 2 => $lang['february'], 3 => $lang['march'],
    4 => $lang['april'], 5 => $lang['may'], 6 => $lang['june'],
    7 => $lang['july'], 8 => $lang['august'], 9 => $lang['september'],
    10 => $lang['october'], 11 => $lang['november'], 12 => $lang['december']
];
?>

<!DOCTYPE html>
<html lang="<?php echo $language; ?>" dir="<?php echo $language === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $lang['calendar']; ?> - <?php echo $lang['appTitle']; ?></title>
    <meta name="description" content="<?php echo $lang['appDescription']; ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a'
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="<?php echo $theme === 'dark' ? 'dark' : ''; ?> bg-gray-50 dark:bg-gray-900">
    <!-- Language Selector -->
    <?php include 'components/language-selector.php'; ?>
    
    <!-- Theme Toggle -->
    <?php include 'components/theme-toggle.php'; ?>
    
    <!-- Sidebar Navigation -->
    <?php include 'components/sidebar-nav.php'; ?>
    
    <div class="<?php echo $language === 'ar' ? 'mr-64' : 'ml-64'; ?> min-h-screen transition-all duration-300">
        <!-- Header -->
        <header class="bg-white dark:bg-gray-800 shadow-sm border-b dark:border-gray-700 px-6 py-4">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                        <?php echo $lang['calendar']; ?>
                    </h1>
                    <p class="text-gray-500 dark:text-gray-400">
                        <?php echo $month_names[$current_month] . ' ' . $current_year; ?>
                    </p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <button onclick="openTaskModal()" class="bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        <?php echo $lang['newTask']; ?>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="p-6">
            <!-- Calendar Navigation -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow mb-6 p-6">
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center gap-4">
                        <a href="?month=<?php echo $prev_month; ?>&year=<?php echo $prev_year; ?>" 
                           class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                        </a>
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                            <?php echo $month_names[$current_month] . ' ' . $current_year; ?>
                        </h2>
                        <a href="?month=<?php echo $next_month; ?>&year=<?php echo $next_year; ?>" 
                           class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                    
                    <div class="flex gap-2">
                        <a href="?month=<?php echo date('n'); ?>&year=<?php echo date('Y'); ?>" 
                           class="px-4 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                            <?php echo $lang['today']; ?>
                        </a>
                    </div>
                </div>

                <!-- Calendar Grid -->
                <div class="grid grid-cols-7 gap-px bg-gray-200 dark:bg-gray-600 rounded-lg overflow-hidden">
                    <!-- Days of week header -->
                    <?php
                    $days_of_week = [$lang['sunday'], $lang['monday'], $lang['tuesday'], $lang['wednesday'], $lang['thursday'], $lang['friday'], $lang['saturday']];
                    foreach ($days_of_week as $day): ?>
                        <div class="bg-gray-50 dark:bg-gray-700 p-3 text-center">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                <?php echo $day; ?>
                            </span>
                        </div>
                    <?php endforeach; ?>

                    <!-- Calendar days -->
                    <?php
                    $first_day = date('w', strtotime("$current_year-$current_month-01"));
                    $days_in_month = date('t', strtotime("$current_year-$current_month-01"));
                    $today = date('Y-m-d');
                    
                    // Previous month's trailing days
                    $prev_month_days = date('t', strtotime("$prev_year-$prev_month-01"));
                    for ($i = $first_day - 1; $i >= 0; $i--) {
                        $day = $prev_month_days - $i;
                        echo '<div class="bg-white dark:bg-gray-800 p-2 h-24 text-gray-400 dark:text-gray-600">';
                        echo '<span class="text-sm">' . $day . '</span>';
                        echo '</div>';
                    }
                    
                    // Current month days
                    for ($day = 1; $day <= $days_in_month; $day++) {
                        $date = sprintf('%04d-%02d-%02d', $current_year, $current_month, $day);
                        $is_today = $date === $today;
                        $has_tasks = isset($calendar_data[$date]);
                        
                        echo '<div class="bg-white dark:bg-gray-800 p-2 h-24 border-l border-t border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer" onclick="selectDate(\'' . $date . '\')">';
                        
                        // Day number
                        echo '<div class="flex items-center justify-between mb-1">';
                        echo '<span class="text-sm font-medium ' . ($is_today ? 'text-primary-600 dark:text-primary-400' : 'text-gray-900 dark:text-white') . '">' . $day . '</span>';
                        if ($is_today) {
                            echo '<span class="w-2 h-2 bg-primary-500 rounded-full"></span>';
                        }
                        echo '</div>';
                        
                        // Tasks indicator
                        if ($has_tasks) {
                            $task_data = $calendar_data[$date];
                            echo '<div class="space-y-1">';
                            echo '<div class="flex items-center gap-1">';
                            echo '<span class="text-xs text-gray-600 dark:text-gray-400">' . $task_data['count'] . ' ' . ($task_data['count'] == 1 ? 'task' : 'tasks') . '</span>';
                            echo '</div>';
                            
                            // Progress bar
                            if ($task_data['count'] > 0) {
                                $completion_rate = ($task_data['completed_count'] / $task_data['count']) * 100;
                                echo '<div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-1">';
                                echo '<div class="bg-green-500 h-1 rounded-full" style="width: ' . $completion_rate . '%"></div>';
                                echo '</div>';
                            }
                        }
                        
                        echo '</div>';
                    }
                    
                    // Next month's leading days
                    $total_cells = $first_day + $days_in_month;
                    $remaining_cells = 42 - $total_cells; // 6 rows * 7 days
                    for ($day = 1; $day <= $remaining_cells && $total_cells < 42; $day++) {
                        echo '<div class="bg-white dark:bg-gray-800 p-2 h-24 text-gray-400 dark:text-gray-600">';
                        echo '<span class="text-sm">' . $day . '</span>';
                        echo '</div>';
                        $total_cells++;
                    }
                    ?>
                </div>
            </div>

            <!-- Selected Date Tasks -->
            <div id="selectedDateTasks" class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 hidden">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4" id="selectedDateTitle">
                    <?php echo $lang['tasksFor'] ?? 'Tasks for'; ?> <span id="selectedDateText"></span>
                </h3>
                <div id="selectedDateTasksList">
                    <!-- Tasks will be loaded here -->
                </div>
            </div>

            <!-- Calendar Legend -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mt-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    <?php echo $lang['legend'] ?? 'Legend'; ?>
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="flex items-center gap-3">
                        <div class="w-4 h-4 bg-primary-500 rounded-full"></div>
                        <span class="text-sm text-gray-600 dark:text-gray-400">
                            <?php echo $lang['today']; ?>
                        </span>
                    </div>
                    <div class="flex items-center gap-3">
                        <div class="w-4 h-1 bg-green-500 rounded-full"></div>
                        <span class="text-sm text-gray-600 dark:text-gray-400">
                            <?php echo $lang['taskProgress'] ?? 'Task Progress'; ?>
                        </span>
                    </div>
                    <div class="flex items-center gap-3">
                        <div class="w-4 h-4 border-2 border-gray-300 rounded"></div>
                        <span class="text-sm text-gray-600 dark:text-gray-400">
                            <?php echo $lang['clickToView'] ?? 'Click to view tasks'; ?>
                        </span>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Task Modal -->
    <?php include 'components/task-modal.php'; ?>

    <!-- Scripts -->
    <script>
        // Pass PHP translations to JavaScript
        window.translations = <?php echo json_encode($lang); ?>;
        window.calendarData = <?php echo json_encode($calendar_data); ?>;
        
        function selectDate(date) {
            const selectedDateTasks = document.getElementById('selectedDateTasks');
            const selectedDateText = document.getElementById('selectedDateText');
            const selectedDateTasksList = document.getElementById('selectedDateTasksList');
            
            // Format date for display
            const dateObj = new Date(date + 'T00:00:00');
            const formattedDate = dateObj.toLocaleDateString('<?php echo $language; ?>', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            
            selectedDateText.textContent = formattedDate;
            
            // Load tasks for selected date
            if (window.calendarData[date]) {
                const dayData = window.calendarData[date];
                const tasks = dayData.tasks_data.split('|');
                
                let tasksHtml = '<div class="space-y-3">';
                tasks.forEach(taskStr => {
                    const [id, title, priority, status] = taskStr.split(':');
                    const priorityClass = priority === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' :
                                        priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                        'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
                    
                    const statusClass = status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                       status === 'in_progress' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' :
                                       'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
                    
                    tasksHtml += `
                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900 dark:text-white ${status === 'completed' ? 'line-through text-gray-500' : ''}">${title}</h4>
                                <div class="flex gap-2 mt-1">
                                    <span class="px-2 py-1 text-xs rounded-full ${priorityClass}">${priority}</span>
                                    <span class="px-2 py-1 text-xs rounded-full ${statusClass}">${status.replace('_', ' ')}</span>
                                </div>
                            </div>
                            <div class="flex gap-1">
                                <button onclick="editTask(${id})" class="text-gray-400 hover:text-blue-500 transition-colors p-1">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    `;
                });
                tasksHtml += '</div>';
                
                selectedDateTasksList.innerHTML = tasksHtml;
            } else {
                selectedDateTasksList.innerHTML = `
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <p class="text-gray-500 dark:text-gray-400 mb-4">${window.translations.noTasksForDate || 'No tasks for this date'}</p>
                        <button onclick="openTaskModalWithDate('${date}')" class="bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-lg transition-colors">
                            ${window.translations.addTask || 'Add Task'}
                        </button>
                    </div>
                `;
            }
            
            selectedDateTasks.classList.remove('hidden');
            selectedDateTasks.scrollIntoView({ behavior: 'smooth' });
        }
        
        function openTaskModalWithDate(date) {
            openTaskModal();
            // Set the due date in the modal
            setTimeout(() => {
                const dueDateInput = document.getElementById('taskDueDate');
                if (dueDateInput) {
                    dueDateInput.value = date;
                }
            }, 100);
        }
    </script>
    <script src="assets/js/theme.js"></script>
    <script src="assets/js/language.js"></script>
    <script src="assets/js/tasks.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>

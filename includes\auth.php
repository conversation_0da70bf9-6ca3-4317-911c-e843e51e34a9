<?php
require_once 'config/db.php';

/**
 * Register a new user
 */
function registerUser($firstName, $lastName, $email, $password) {
    $pdo = getDB();
    
    try {
        // Check if email already exists
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$email]);
        if ($stmt->fetch()) {
            return "Email address is already registered.";
        }
        
        // Hash password
        $passwordHash = password_hash($password, PASSWORD_DEFAULT);
        
        // Insert new user
        $stmt = $pdo->prepare("
            INSERT INTO users (first_name, last_name, email, password_hash) 
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([$firstName, $lastName, $email, $passwordHash]);
        
        $userId = $pdo->lastInsertId();
        
        // Create default user preferences
        $stmt = $pdo->prepare("
            INSERT INTO user_preferences (user_id, theme, language) 
            VALUES (?, 'light', 'ar')
        ");
        $stmt->execute([$userId]);
        
        return true;
        
    } catch (PDOException $e) {
        error_log("Registration error: " . $e->getMessage());
        return "Registration failed. Please try again.";
    }
}

/**
 * Login user
 */
function loginUser($email, $password) {
    $pdo = getDB();
    
    try {
        $stmt = $pdo->prepare("SELECT id, password_hash FROM users WHERE email = ?");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password_hash'])) {
            return $user;
        }
        
        return false;
        
    } catch (PDOException $e) {
        error_log("Login error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get user by ID
 */
function getUserById($userId) {
    $pdo = getDB();
    
    try {
        $stmt = $pdo->prepare("
            SELECT id, first_name, last_name, email, profile_image_url, created_at 
            FROM users WHERE id = ?
        ");
        $stmt->execute([$userId]);
        return $stmt->fetch();
        
    } catch (PDOException $e) {
        error_log("Get user error: " . $e->getMessage());
        return null;
    }
}

/**
 * Get user preferences
 */
function getUserPreferences($userId) {
    $pdo = getDB();
    
    try {
        $stmt = $pdo->prepare("SELECT theme, language FROM user_preferences WHERE user_id = ?");
        $stmt->execute([$userId]);
        $preferences = $stmt->fetch();
        
        if (!$preferences) {
            // Create default preferences if they don't exist
            $stmt = $pdo->prepare("
                INSERT INTO user_preferences (user_id, theme, language) 
                VALUES (?, 'light', 'ar')
            ");
            $stmt->execute([$userId]);
            return ['theme' => 'light', 'language' => 'ar'];
        }
        
        return $preferences;
        
    } catch (PDOException $e) {
        error_log("Get preferences error: " . $e->getMessage());
        return ['theme' => 'light', 'language' => 'ar'];
    }
}

/**
 * Update user preferences
 */
function updateUserPreferences($userId, $theme, $language) {
    $pdo = getDB();
    
    try {
        $stmt = $pdo->prepare("
            UPDATE user_preferences
            SET theme = ?, language = ?, updated_at = NOW()
            WHERE user_id = ?
        ");
        return $stmt->execute([$theme, $language, $userId]);
        
    } catch (PDOException $e) {
        error_log("Update preferences error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get today's tasks for user
 */
function getTodayTasks($userId) {
    $pdo = getDB();
    
    try {
        $today = date('Y-m-d');
        $stmt = $pdo->prepare("
            SELECT * FROM tasks 
            WHERE user_id = ? AND DATE(due_date) = ? 
            ORDER BY 
                CASE priority 
                    WHEN 'high' THEN 1 
                    WHEN 'medium' THEN 2 
                    WHEN 'low' THEN 3 
                END,
                created_at DESC
        ");
        $stmt->execute([$userId, $today]);
        return $stmt->fetchAll();
        
    } catch (PDOException $e) {
        error_log("Get today tasks error: " . $e->getMessage());
        return [];
    }
}

/**
 * Get task statistics for user
 */
function getTaskStats($userId) {
    $pdo = getDB();
    
    try {
        $today = date('Y-m-d');
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
                COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending
            FROM tasks 
            WHERE user_id = ? AND DATE(due_date) = ?
        ");
        $stmt->execute([$userId, $today]);
        $stats = $stmt->fetch();
        
        $completionRate = $stats['total'] > 0 ? round(($stats['completed'] / $stats['total']) * 100) : 0;
        
        return [
            'total' => (int)$stats['total'],
            'completed' => (int)$stats['completed'],
            'in_progress' => (int)$stats['in_progress'],
            'pending' => (int)$stats['pending'],
            'completion_rate' => $completionRate
        ];
        
    } catch (PDOException $e) {
        error_log("Get task stats error: " . $e->getMessage());
        return ['total' => 0, 'completed' => 0, 'in_progress' => 0, 'pending' => 0, 'completion_rate' => 0];
    }
}

/**
 * Get weekly progress for user
 */
function getWeeklyProgress($userId) {
    $pdo = getDB();
    
    try {
        $stmt = $pdo->prepare("
            SELECT
                DATE(due_date) as date,
                COUNT(*) as total,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed
            FROM tasks
            WHERE user_id = ? AND due_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            GROUP BY DATE(due_date)
            ORDER BY DATE(due_date)
        ");
        $stmt->execute([$userId]);
        return $stmt->fetchAll();
        
    } catch (PDOException $e) {
        error_log("Get weekly progress error: " . $e->getMessage());
        return [];
    }
}

/**
 * Create new task
 */
function createTask($userId, $title, $description, $priority, $dueDate) {
    $pdo = getDB();
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO tasks (user_id, title, description, priority, due_date) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([$userId, $title, $description, $priority, $dueDate]);
        return $pdo->lastInsertId();
        
    } catch (PDOException $e) {
        error_log("Create task error: " . $e->getMessage());
        return false;
    }
}

/**
 * Update task
 */
function updateTask($taskId, $userId, $data) {
    $pdo = getDB();
    
    try {
        $allowedFields = ['title', 'description', 'priority', 'status', 'due_date'];
        $updates = [];
        $values = [];
        
        foreach ($data as $field => $value) {
            if (in_array($field, $allowedFields)) {
                $updates[] = "$field = ?";
                $values[] = $value;
            }
        }
        
        if (empty($updates)) {
            return false;
        }
        
        $values[] = $userId;
        $values[] = $taskId;
        
        $stmt = $pdo->prepare("
            UPDATE tasks
            SET " . implode(', ', $updates) . ", updated_at = NOW()
            WHERE user_id = ? AND id = ?
        ");
        
        return $stmt->execute($values);
        
    } catch (PDOException $e) {
        error_log("Update task error: " . $e->getMessage());
        return false;
    }
}

/**
 * Delete task
 */
function deleteTask($taskId, $userId) {
    $pdo = getDB();
    
    try {
        $stmt = $pdo->prepare("DELETE FROM tasks WHERE id = ? AND user_id = ?");
        return $stmt->execute([$taskId, $userId]);
        
    } catch (PDOException $e) {
        error_log("Delete task error: " . $e->getMessage());
        return false;
    }
}

/**
 * Mark task as completed
 */
function completeTask($taskId, $userId) {
    $pdo = getDB();
    
    try {
        $stmt = $pdo->prepare("
            UPDATE tasks
            SET status = 'completed', completed_at = NOW(), updated_at = NOW()
            WHERE id = ? AND user_id = ?
        ");
        return $stmt->execute([$taskId, $userId]);
        
    } catch (PDOException $e) {
        error_log("Complete task error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get random quote by language
 */
function getRandomQuote($language = 'ar') {
    $pdo = getDB();
    
    try {
        $stmt = $pdo->prepare("SELECT text, author FROM quotes WHERE language = ? ORDER BY RAND() LIMIT 1");
        $stmt->execute([$language]);
        return $stmt->fetch();
        
    } catch (PDOException $e) {
        error_log("Get quote error: " . $e->getMessage());
        return null;
    }
}
?>
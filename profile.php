<?php
session_start();
require_once 'config/db.php';
require_once 'includes/auth.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// Get user data and preferences
$user = getUserById($_SESSION['user_id']);
$preferences = getUserPreferences($_SESSION['user_id']);
$theme = $preferences['theme'] ?? 'light';
$language = $preferences['language'] ?? 'ar';

// Include language file
require_once "lang/{$language}.php";

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_profile':
                $firstName = trim($_POST['first_name'] ?? '');
                $lastName = trim($_POST['last_name'] ?? '');
                $email = trim($_POST['email'] ?? '');
                
                if (empty($firstName) || empty($lastName) || empty($email)) {
                    $error = $lang['fillAllFields'] ?? 'Please fill in all fields.';
                } else {
                    try {
                        $pdo = getDB();
                        
                        // Check if email is already taken by another user
                        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
                        $stmt->execute([$email, $_SESSION['user_id']]);
                        if ($stmt->fetch()) {
                            $error = $lang['emailTaken'] ?? 'Email address is already taken.';
                        } else {
                            // Update user profile
                            $stmt = $pdo->prepare("UPDATE users SET first_name = ?, last_name = ?, email = ?, updated_at = NOW() WHERE id = ?");
                            if ($stmt->execute([$firstName, $lastName, $email, $_SESSION['user_id']])) {
                                $success = $lang['profileUpdated'] ?? 'Profile updated successfully.';
                                $user = getUserById($_SESSION['user_id']); // Refresh user data
                            } else {
                                $error = $lang['updateFailed'] ?? 'Failed to update profile.';
                            }
                        }
                    } catch (PDOException $e) {
                        error_log("Profile update error: " . $e->getMessage());
                        $error = $lang['updateFailed'] ?? 'Failed to update profile.';
                    }
                }
                break;
                
            case 'change_password':
                $currentPassword = $_POST['current_password'] ?? '';
                $newPassword = $_POST['new_password'] ?? '';
                $confirmPassword = $_POST['confirm_password'] ?? '';
                
                if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
                    $error = $lang['fillAllFields'] ?? 'Please fill in all fields.';
                } elseif ($newPassword !== $confirmPassword) {
                    $error = $lang['passwordsNotMatch'] ?? 'New passwords do not match.';
                } elseif (strlen($newPassword) < 6) {
                    $error = $lang['passwordTooShort'] ?? 'Password must be at least 6 characters long.';
                } else {
                    try {
                        $pdo = getDB();
                        $stmt = $pdo->prepare("SELECT password_hash FROM users WHERE id = ?");
                        $stmt->execute([$_SESSION['user_id']]);
                        $user_data = $stmt->fetch();
                        
                        if ($user_data && password_verify($currentPassword, $user_data['password_hash'])) {
                            $newPasswordHash = password_hash($newPassword, PASSWORD_DEFAULT);
                            $stmt = $pdo->prepare("UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?");
                            if ($stmt->execute([$newPasswordHash, $_SESSION['user_id']])) {
                                $success = $lang['passwordChanged'] ?? 'Password changed successfully.';
                            } else {
                                $error = $lang['passwordChangeFailed'] ?? 'Failed to change password.';
                            }
                        } else {
                            $error = $lang['currentPasswordIncorrect'] ?? 'Current password is incorrect.';
                        }
                    } catch (PDOException $e) {
                        error_log("Password change error: " . $e->getMessage());
                        $error = $lang['passwordChangeFailed'] ?? 'Failed to change password.';
                    }
                }
                break;
                
            case 'update_preferences':
                $newTheme = $_POST['theme'] ?? 'light';
                $newLanguage = $_POST['language'] ?? 'ar';
                
                if (!in_array($newTheme, ['light', 'dark'])) {
                    $newTheme = 'light';
                }
                if (!in_array($newLanguage, ['ar', 'en', 'fr'])) {
                    $newLanguage = 'ar';
                }
                
                if (updateUserPreferences($_SESSION['user_id'], $newTheme, $newLanguage)) {
                    $success = $lang['preferencesUpdated'] ?? 'Preferences updated successfully.';
                    // Redirect to apply new language
                    header("Location: profile.php?updated=1");
                    exit();
                } else {
                    $error = $lang['preferencesUpdateFailed'] ?? 'Failed to update preferences.';
                }
                break;
        }
    }
}

// Check for update success message
if (isset($_GET['updated'])) {
    $success = $lang['preferencesUpdated'] ?? 'Preferences updated successfully.';
}

// Get user statistics
try {
    $pdo = getDB();
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_tasks,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_tasks,
            COUNT(CASE WHEN DATE(due_date) = CURDATE() THEN 1 END) as today_tasks
        FROM tasks WHERE user_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $stats = $stmt->fetch();
} catch (PDOException $e) {
    $stats = ['total_tasks' => 0, 'completed_tasks' => 0, 'today_tasks' => 0];
}
?>

<!DOCTYPE html>
<html lang="<?php echo $language; ?>" dir="<?php echo $language === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $lang['profile'] ?? 'Profile'; ?> - <?php echo $lang['appTitle']; ?></title>
    <meta name="description" content="<?php echo $lang['appDescription']; ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a'
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="<?php echo $theme === 'dark' ? 'dark' : ''; ?> bg-gray-50 dark:bg-gray-900">
    <!-- Language Selector -->
    <?php include 'components/language-selector.php'; ?>
    
    <!-- Theme Toggle -->
    <?php include 'components/theme-toggle.php'; ?>
    
    <!-- Sidebar Navigation -->
    <?php include 'components/sidebar-nav.php'; ?>
    
    <div class="<?php echo $language === 'ar' ? 'mr-64' : 'ml-64'; ?> min-h-screen transition-all duration-300">
        <!-- Header -->
        <header class="bg-white dark:bg-gray-800 shadow-sm border-b dark:border-gray-700 px-6 py-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                        <?php echo $lang['profile'] ?? 'Profile'; ?>
                    </h1>
                    <p class="text-gray-500 dark:text-gray-400">
                        <?php echo $lang['manageAccount'] ?? 'Manage your account settings and preferences'; ?>
                    </p>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="p-6">
            <!-- Success/Error Messages -->
            <?php if ($error): ?>
                <div class="mb-6 p-4 bg-red-100 dark:bg-red-900 border border-red-300 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg">
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="mb-6 p-4 bg-green-100 dark:bg-green-900 border border-green-300 dark:border-green-700 text-green-700 dark:text-green-300 rounded-lg">
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Profile Overview -->
                <div class="lg:col-span-1">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <div class="text-center">
                            <div class="w-20 h-20 bg-primary-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span class="text-2xl font-bold text-white">
                                    <?php echo strtoupper(substr($user['first_name'] ?: $user['email'], 0, 1)); ?>
                                </span>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                            </h3>
                            <p class="text-gray-500 dark:text-gray-400">
                                <?php echo htmlspecialchars($user['email']); ?>
                            </p>
                            <p class="text-sm text-gray-400 dark:text-gray-500 mt-2">
                                <?php echo $lang['memberSince'] ?? 'Member since'; ?> <?php echo date('M Y', strtotime($user['created_at'])); ?>
                            </p>
                        </div>

                        <!-- Quick Stats -->
                        <div class="mt-6 pt-6 border-t dark:border-gray-700">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">
                                <?php echo $lang['quickStats'] ?? 'Quick Stats'; ?>
                            </h4>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500 dark:text-gray-400"><?php echo $lang['totalTasks'] ?? 'Total Tasks'; ?></span>
                                    <span class="text-sm font-medium text-gray-900 dark:text-white"><?php echo $stats['total_tasks']; ?></span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500 dark:text-gray-400"><?php echo $lang['completed']; ?></span>
                                    <span class="text-sm font-medium text-gray-900 dark:text-white"><?php echo $stats['completed_tasks']; ?></span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500 dark:text-gray-400"><?php echo $lang['todayTasks']; ?></span>
                                    <span class="text-sm font-medium text-gray-900 dark:text-white"><?php echo $stats['today_tasks']; ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Forms -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Profile Information -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            <?php echo $lang['profileInformation'] ?? 'Profile Information'; ?>
                        </h3>
                        <form method="POST" class="space-y-4">
                            <input type="hidden" name="action" value="update_profile">
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        <?php echo $lang['firstName']; ?>
                                    </label>
                                    <input type="text" name="first_name" value="<?php echo htmlspecialchars($user['first_name']); ?>" required
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        <?php echo $lang['lastName']; ?>
                                    </label>
                                    <input type="text" name="last_name" value="<?php echo htmlspecialchars($user['last_name']); ?>" required
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    <?php echo $lang['email']; ?>
                                </label>
                                <input type="email" name="email" value="<?php echo htmlspecialchars($user['email']); ?>" required
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            </div>
                            
                            <button type="submit" class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-2 rounded-lg transition-colors">
                                <?php echo $lang['updateProfile'] ?? 'Update Profile'; ?>
                            </button>
                        </form>
                    </div>

                    <!-- Change Password -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            <?php echo $lang['changePassword'] ?? 'Change Password'; ?>
                        </h3>
                        <form method="POST" class="space-y-4">
                            <input type="hidden" name="action" value="change_password">
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    <?php echo $lang['currentPassword'] ?? 'Current Password'; ?>
                                </label>
                                <input type="password" name="current_password" required
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    <?php echo $lang['newPassword'] ?? 'New Password'; ?>
                                </label>
                                <input type="password" name="new_password" required
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    <?php echo $lang['confirmPassword']; ?>
                                </label>
                                <input type="password" name="confirm_password" required
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            </div>
                            
                            <button type="submit" class="bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-lg transition-colors">
                                <?php echo $lang['changePassword'] ?? 'Change Password'; ?>
                            </button>
                        </form>
                    </div>

                    <!-- Preferences -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            <?php echo $lang['preferences'] ?? 'Preferences'; ?>
                        </h3>
                        <form method="POST" class="space-y-4">
                            <input type="hidden" name="action" value="update_preferences">
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        <?php echo $lang['theme'] ?? 'Theme'; ?>
                                    </label>
                                    <select name="theme" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                        <option value="light" <?php echo $theme === 'light' ? 'selected' : ''; ?>><?php echo $lang['lightTheme'] ?? 'Light'; ?></option>
                                        <option value="dark" <?php echo $theme === 'dark' ? 'selected' : ''; ?>><?php echo $lang['darkTheme'] ?? 'Dark'; ?></option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        <?php echo $lang['language'] ?? 'Language'; ?>
                                    </label>
                                    <select name="language" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                        <option value="ar" <?php echo $language === 'ar' ? 'selected' : ''; ?>>العربية</option>
                                        <option value="en" <?php echo $language === 'en' ? 'selected' : ''; ?>>English</option>
                                        <option value="fr" <?php echo $language === 'fr' ? 'selected' : ''; ?>>Français</option>
                                    </select>
                                </div>
                            </div>
                            
                            <button type="submit" class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-2 rounded-lg transition-colors">
                                <?php echo $lang['updatePreferences'] ?? 'Update Preferences'; ?>
                            </button>
                        </form>
                    </div>

                    <!-- Danger Zone -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 border-l-4 border-red-500">
                        <h3 class="text-lg font-semibold text-red-600 dark:text-red-400 mb-4">
                            <?php echo $lang['dangerZone'] ?? 'Danger Zone'; ?>
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                            <?php echo $lang['dangerZoneDescription'] ?? 'These actions cannot be undone. Please be careful.'; ?>
                        </p>
                        <button onclick="confirmDeleteAccount()" class="bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-lg transition-colors">
                            <?php echo $lang['deleteAccount'] ?? 'Delete Account'; ?>
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script>
        // Pass PHP translations to JavaScript
        window.translations = <?php echo json_encode($lang); ?>;
        
        function confirmDeleteAccount() {
            if (confirm(window.translations.confirmDeleteAccount || 'Are you sure you want to delete your account? This action cannot be undone.')) {
                if (confirm(window.translations.confirmDeleteAccountFinal || 'This will permanently delete all your data. Are you absolutely sure?')) {
                    // Implement account deletion
                    alert(window.translations.featureComingSoon || 'This feature will be implemented soon.');
                }
            }
        }
    </script>
    <script src="assets/js/theme.js"></script>
    <script src="assets/js/language.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>

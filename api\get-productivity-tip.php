<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

session_start();
require_once '../config/db.php';
require_once '../includes/auth.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

try {
    // Get user preferences for language
    $preferences = getUserPreferences($_SESSION['user_id']);
    $language = $preferences['language'] ?? 'ar';
    
    // Get category filter if provided
    $category = $_GET['category'] ?? null;
    
    $pdo = getDB();
    
    // Build query based on filters
    $sql = "SELECT title, content, category FROM productivity_tips WHERE language = ?";
    $params = [$language];
    
    if ($category) {
        $sql .= " AND category = ?";
        $params[] = $category;
    }
    
    $sql .= " ORDER BY RAND() LIMIT 1";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $tip = $stmt->fetch();
    
    if ($tip) {
        echo json_encode([
            'success' => true,
            'tip' => [
                'title' => $tip['title'],
                'content' => $tip['content'],
                'category' => $tip['category']
            ]
        ]);
    } else {
        // Fallback to English if no tips in preferred language
        $stmt = $pdo->prepare("SELECT title, content, category FROM productivity_tips WHERE language = 'en' ORDER BY RAND() LIMIT 1");
        $stmt->execute();
        $tip = $stmt->fetch();
        
        if ($tip) {
            echo json_encode([
                'success' => true,
                'tip' => [
                    'title' => $tip['title'],
                    'content' => $tip['content'],
                    'category' => $tip['category']
                ]
            ]);
        } else {
            // Ultimate fallback
            echo json_encode([
                'success' => true,
                'tip' => [
                    'title' => 'Pomodoro Technique',
                    'content' => 'Work for 25 minutes, then take a 5-minute break. After 4 cycles, take a longer 15-30 minute break.',
                    'category' => 'time_management'
                ]
            ]);
        }
    }
    
} catch (PDOException $e) {
    error_log("Productivity tip API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database error',
        'tip' => [
            'title' => 'Two-Minute Rule',
            'content' => 'If a task takes less than 2 minutes to complete, do it immediately instead of adding it to your task list.',
            'category' => 'efficiency'
        ]
    ]);
}
?>

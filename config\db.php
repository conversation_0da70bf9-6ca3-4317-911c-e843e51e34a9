<?php
// Database configuration using SQLite
$dbPath = __DIR__ . '/../database/productivity_app.db';

// Create database directory if it doesn't exist
$dbDir = dirname($dbPath);
if (!is_dir($dbDir)) {
    mkdir($dbDir, 0755, true);
}

try {
    $pdo = new PDO("sqlite:$dbPath");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
    // Enable foreign key constraints
    $pdo->exec("PRAGMA foreign_keys = ON");
    
    // Create tables if they don't exist
    createTables($pdo);
    
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

function createTables($pdo) {
    try {
        // Users table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                first_name TEXT,
                last_name TEXT,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                profile_image_url TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ");

        // User preferences table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS user_preferences (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                theme TEXT DEFAULT 'light',
                language TEXT DEFAULT 'ar',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE(user_id)
            )
        ");

        // Tasks table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                title TEXT NOT NULL,
                description TEXT,
                priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
                status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed')),
                due_date DATETIME,
                completed_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        ");

        // Quotes table for AI assistant
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS quotes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                text TEXT NOT NULL,
                author TEXT,
                language TEXT DEFAULT 'ar',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ");

        // Insert default quotes if table is empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM quotes");
        if ($stmt->fetchColumn() == 0) {
            insertDefaultQuotes($pdo);
        }

    } catch (PDOException $e) {
        error_log("Table creation failed: " . $e->getMessage());
    }
}

function insertDefaultQuotes($pdo) {
    $quotes = [
        // Arabic quotes
        ['النجاح هو المشي من فشل إلى فشل بلا فقدان للحماس.', 'ونستون تشرشل', 'ar'],
        ['الطريق إلى النجاح يبدأ بخطوة واحدة.', 'لاو تزو', 'ar'],
        ['لا تؤجل عمل اليوم إلى الغد، فالغد قد لا يأتي أبداً.', 'بنجامين فرانكلين', 'ar'],
        ['الإنتاجية ليست في كثرة العمل، بل في إنجاز الأهم أولاً.', 'ستيفن كوفي', 'ar'],
        
        // English quotes
        ['Success is walking from failure to failure with no loss of enthusiasm.', 'Winston Churchill', 'en'],
        ['The way to get started is to quit talking and begin doing.', 'Walt Disney', 'en'],  
        ['Don\'t put off until tomorrow what you can do today.', 'Benjamin Franklin', 'en'],
        ['Productivity is not about doing more, but about doing what matters most.', 'Stephen Covey', 'en'],
        
        // French quotes
        ['Le succès c\'est marcher d\'échec en échec tout en gardant son enthousiasme.', 'Winston Churchill', 'fr'],
        ['La façon de commencer est d\'arrêter de parler et de commencer à faire.', 'Walt Disney', 'fr'],
        ['Ne remets pas à demain ce que tu peux faire aujourd\'hui.', 'Benjamin Franklin', 'fr'],
        ['La productivité ne consiste pas à faire plus, mais à faire ce qui compte le plus.', 'Stephen Covey', 'fr']
    ];

    $stmt = $pdo->prepare("INSERT INTO quotes (text, author, language) VALUES (?, ?, ?)");
    foreach ($quotes as $quote) {
        $stmt->execute($quote);
    }
}

// Helper function to get database connection
function getDB() {
    global $pdo;
    return $pdo;
}
?>
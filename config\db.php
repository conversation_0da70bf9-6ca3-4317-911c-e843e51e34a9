<?php
// Database configuration using MySQL
$host = 'localhost';
$dbname = 'lifepilot_db';
$username = 'root';  // Change this to your MySQL username
$password = '';      // Change this to your MySQL password
$charset = 'utf8mb4';

$dsn = "mysql:host=$host;dbname=$dbname;charset=$charset";

$options = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES   => false,
];

try {
    $pdo = new PDO($dsn, $username, $password, $options);

    // Create tables if they don't exist
    createTables($pdo);

} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

function createTables($pdo) {
    try {
        // Users table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                first_name VARCHA<PERSON>(100),
                last_name VARCHAR(100),
                email VARCHAR(255) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                profile_image_url TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // User preferences table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS user_preferences (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT,
                theme VARCHAR(20) DEFAULT 'light',
                language VARCHAR(10) DEFAULT 'ar',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY unique_user_id (user_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // Tasks table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS tasks (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT,
                title VARCHAR(500) NOT NULL,
                description TEXT,
                priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
                status ENUM('pending', 'in_progress', 'completed') DEFAULT 'pending',
                due_date DATETIME,
                completed_at DATETIME,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_user_due_date (user_id, due_date),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // Quotes table for AI assistant
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS quotes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                text TEXT NOT NULL,
                author VARCHAR(255),
                language VARCHAR(10) DEFAULT 'ar',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_language (language)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // AI Conversations table for storing chat history
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS ai_conversations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT,
                message TEXT NOT NULL,
                response TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_user_created (user_id, created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // Productivity tips table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS productivity_tips (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                content TEXT NOT NULL,
                category VARCHAR(100),
                language VARCHAR(10) DEFAULT 'ar',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_language_category (language, category)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // Insert default quotes if table is empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM quotes");
        if ($stmt->fetchColumn() == 0) {
            insertDefaultQuotes($pdo);
        }

        // Insert default productivity tips if table is empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM productivity_tips");
        if ($stmt->fetchColumn() == 0) {
            insertDefaultTips($pdo);
        }

    } catch (PDOException $e) {
        error_log("Table creation failed: " . $e->getMessage());
    }
}

function insertDefaultQuotes($pdo) {
    $quotes = [
        // Arabic quotes
        ['النجاح هو المشي من فشل إلى فشل بلا فقدان للحماس.', 'ونستون تشرشل', 'ar'],
        ['الطريق إلى النجاح يبدأ بخطوة واحدة.', 'لاو تزو', 'ar'],
        ['لا تؤجل عمل اليوم إلى الغد، فالغد قد لا يأتي أبداً.', 'بنجامين فرانكلين', 'ar'],
        ['الإنتاجية ليست في كثرة العمل، بل في إنجاز الأهم أولاً.', 'ستيفن كوفي', 'ar'],
        
        // English quotes
        ['Success is walking from failure to failure with no loss of enthusiasm.', 'Winston Churchill', 'en'],
        ['The way to get started is to quit talking and begin doing.', 'Walt Disney', 'en'],  
        ['Don\'t put off until tomorrow what you can do today.', 'Benjamin Franklin', 'en'],
        ['Productivity is not about doing more, but about doing what matters most.', 'Stephen Covey', 'en'],
        
        // French quotes
        ['Le succès c\'est marcher d\'échec en échec tout en gardant son enthousiasme.', 'Winston Churchill', 'fr'],
        ['La façon de commencer est d\'arrêter de parler et de commencer à faire.', 'Walt Disney', 'fr'],
        ['Ne remets pas à demain ce que tu peux faire aujourd\'hui.', 'Benjamin Franklin', 'fr'],
        ['La productivité ne consiste pas à faire plus, mais à faire ce qui compte le plus.', 'Stephen Covey', 'fr']
    ];

    $stmt = $pdo->prepare("INSERT INTO quotes (text, author, language) VALUES (?, ?, ?)");
    foreach ($quotes as $quote) {
        $stmt->execute($quote);
    }
}

function insertDefaultTips($pdo) {
    $tips = [
        // English tips
        ['Pomodoro Technique', 'Work for 25 minutes, then take a 5-minute break. After 4 cycles, take a longer 15-30 minute break.', 'time_management', 'en'],
        ['Eisenhower Matrix', 'Categorize tasks by importance and urgency: Do (urgent+important), Decide (important), Delegate (urgent), Delete (neither).', 'prioritization', 'en'],
        ['Two-Minute Rule', 'If a task takes less than 2 minutes to complete, do it immediately instead of adding it to your task list.', 'efficiency', 'en'],
        ['Time Blocking', 'Schedule specific time blocks for different types of work. This helps maintain focus and prevents multitasking.', 'time_management', 'en'],
        ['Eat the Frog', 'Complete your most challenging or important task first thing in the morning when your energy is highest.', 'motivation', 'en'],

        // Arabic tips
        ['تقنية البومودورو', 'اعمل لمدة 25 دقيقة، ثم خذ استراحة 5 دقائق. بعد 4 دورات، خذ استراحة أطول من 15-30 دقيقة.', 'time_management', 'ar'],
        ['مصفوفة أيزنهاور', 'صنف المهام حسب الأهمية والإلحاح: افعل (عاجل+مهم)، قرر (مهم)، فوض (عاجل)، احذف (لا شيء).', 'prioritization', 'ar'],
        ['قاعدة الدقيقتين', 'إذا كانت المهمة تستغرق أقل من دقيقتين، افعلها فوراً بدلاً من إضافتها لقائمة المهام.', 'efficiency', 'ar'],
        ['تخصيص الوقت', 'جدول كتل زمنية محددة لأنواع مختلفة من العمل. هذا يساعد في الحفاظ على التركيز ومنع تعدد المهام.', 'time_management', 'ar'],
        ['ابدأ بالأصعب', 'أكمل مهمتك الأكثر تحدياً أو أهمية أول شيء في الصباح عندما تكون طاقتك في أعلى مستوياتها.', 'motivation', 'ar'],

        // French tips
        ['Technique Pomodoro', 'Travaillez pendant 25 minutes, puis prenez une pause de 5 minutes. Après 4 cycles, prenez une pause plus longue de 15-30 minutes.', 'time_management', 'fr'],
        ['Matrice d\'Eisenhower', 'Catégorisez les tâches par importance et urgence: Faire (urgent+important), Décider (important), Déléguer (urgent), Supprimer (ni l\'un ni l\'autre).', 'prioritization', 'fr'],
        ['Règle des 2 minutes', 'Si une tâche prend moins de 2 minutes à accomplir, faites-la immédiatement au lieu de l\'ajouter à votre liste de tâches.', 'efficiency', 'fr']
    ];

    $stmt = $pdo->prepare("INSERT INTO productivity_tips (title, content, category, language) VALUES (?, ?, ?, ?)");

    foreach ($tips as $tip) {
        $stmt->execute($tip);
    }
}

// Helper function to get database connection
function getDB() {
    global $pdo;
    return $pdo;
}
?>
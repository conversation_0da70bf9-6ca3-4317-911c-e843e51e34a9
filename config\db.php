<?php
// Database configuration using MySQL
$host = 'localhost';
$dbname = 'lifepilot_db';
$username = 'root';  // Change this to your MySQL username
$password = '';      // Change this to your MySQL password
$charset = 'utf8mb4';

$dsn = "mysql:host=$host;dbname=$dbname;charset=$charset";

$options = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES   => false,
];

try {
    $pdo = new PDO($dsn, $username, $password, $options);

    // Create tables if they don't exist
    createTables($pdo);

} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

function createTables($pdo) {
    try {
        // Users table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                first_name VARCHA<PERSON>(100),
                last_name VARCHAR(100),
                email VARCHAR(255) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                profile_image_url TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // User preferences table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS user_preferences (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT,
                theme VARCHAR(20) DEFAULT 'light',
                language VARCHAR(10) DEFAULT 'ar',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY unique_user_id (user_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // Tasks table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS tasks (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT,
                title VARCHAR(500) NOT NULL,
                description TEXT,
                priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
                status ENUM('pending', 'in_progress', 'completed') DEFAULT 'pending',
                due_date DATETIME,
                completed_at DATETIME,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_user_due_date (user_id, due_date),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // Quotes table for AI assistant
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS quotes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                text TEXT NOT NULL,
                author VARCHAR(255),
                language VARCHAR(10) DEFAULT 'ar',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_language (language)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // Insert default quotes if table is empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM quotes");
        if ($stmt->fetchColumn() == 0) {
            insertDefaultQuotes($pdo);
        }

    } catch (PDOException $e) {
        error_log("Table creation failed: " . $e->getMessage());
    }
}

function insertDefaultQuotes($pdo) {
    $quotes = [
        // Arabic quotes
        ['النجاح هو المشي من فشل إلى فشل بلا فقدان للحماس.', 'ونستون تشرشل', 'ar'],
        ['الطريق إلى النجاح يبدأ بخطوة واحدة.', 'لاو تزو', 'ar'],
        ['لا تؤجل عمل اليوم إلى الغد، فالغد قد لا يأتي أبداً.', 'بنجامين فرانكلين', 'ar'],
        ['الإنتاجية ليست في كثرة العمل، بل في إنجاز الأهم أولاً.', 'ستيفن كوفي', 'ar'],
        
        // English quotes
        ['Success is walking from failure to failure with no loss of enthusiasm.', 'Winston Churchill', 'en'],
        ['The way to get started is to quit talking and begin doing.', 'Walt Disney', 'en'],  
        ['Don\'t put off until tomorrow what you can do today.', 'Benjamin Franklin', 'en'],
        ['Productivity is not about doing more, but about doing what matters most.', 'Stephen Covey', 'en'],
        
        // French quotes
        ['Le succès c\'est marcher d\'échec en échec tout en gardant son enthousiasme.', 'Winston Churchill', 'fr'],
        ['La façon de commencer est d\'arrêter de parler et de commencer à faire.', 'Walt Disney', 'fr'],
        ['Ne remets pas à demain ce que tu peux faire aujourd\'hui.', 'Benjamin Franklin', 'fr'],
        ['La productivité ne consiste pas à faire plus, mais à faire ce qui compte le plus.', 'Stephen Covey', 'fr']
    ];

    $stmt = $pdo->prepare("INSERT INTO quotes (text, author, language) VALUES (?, ?, ?)");
    foreach ($quotes as $quote) {
        $stmt->execute($quote);
    }
}

// Helper function to get database connection
function getDB() {
    global $pdo;
    return $pdo;
}
?>
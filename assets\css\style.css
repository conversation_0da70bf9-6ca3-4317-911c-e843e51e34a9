/* Custom CSS for PHP Productivity App */

/* RTL Support */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] .sidebar {
    right: 0;
    left: auto;
}

[dir="rtl"] .main-content {
    margin-right: 16rem;
    margin-left: 0;
}

/* Dark mode transitions */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

.dark ::-webkit-scrollbar-track {
    background: #374151;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.dark ::-webkit-scrollbar-thumb {
    background: #6b7280;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.dark ::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Loading animation */
@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* Pulse animation for loading states */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Task priority indicators */
.priority-high {
    border-left: 4px solid #ef4444;
}

.priority-medium {
    border-left: 4px solid #f59e0b;
}

.priority-low {
    border-left: 4px solid #10b981;
}

/* RTL priority indicators */
[dir="rtl"] .priority-high {
    border-left: none;
    border-right: 4px solid #ef4444;
}

[dir="rtl"] .priority-medium {
    border-left: none;
    border-right: 4px solid #f59e0b;
}

[dir="rtl"] .priority-low {
    border-left: none;
    border-right: 4px solid #10b981;
}

/* Calendar styles */
.calendar-day {
    @apply w-8 h-8 flex items-center justify-center text-sm rounded cursor-pointer transition-colors;
}

.calendar-day:hover {
    @apply bg-gray-100 dark:bg-gray-700;
}

.calendar-day.today {
    @apply bg-primary-500 text-white;
}

.calendar-day.has-tasks {
    @apply bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200;
}

.calendar-day.other-month {
    @apply text-gray-400 dark:text-gray-600;
}

/* Success and error message animations */
.message-enter {
    opacity: 0;
    transform: translateY(-10px);
}

.message-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.message-exit {
    opacity: 1;
    transform: translateY(0);
}

.message-exit-active {
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Form validation styles */
.field-error {
    @apply border-red-500 dark:border-red-400;
}

.field-error:focus {
    @apply ring-red-500 dark:ring-red-400;
}

/* Modal backdrop animation */
.modal-backdrop {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.modal-content {
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Progress bars */
.progress-bar {
    overflow: hidden;
    background: #e5e7eb;
    border-radius: 9999px;
    height: 8px;
}

.dark .progress-bar {
    background: #374151;
}

.progress-fill {
    height: 100%;
    border-radius: 9999px;
    transition: width 0.5s ease;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

/* Responsive design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    [dir="rtl"] .sidebar {
        transform: translateX(100%);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        margin-right: 0;
    }
    
    [dir="rtl"] .main-content {
        margin-left: 0;
        margin-right: 0;
    }
}

/* Custom button styles */
.btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-colors;
}

.btn-secondary {
    @apply bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors;
}

.btn-success {
    @apply bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-medium transition-colors;
}

.btn-danger {
    @apply bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg font-medium transition-colors;
}

/* Print styles */
@media print {
    .sidebar,
    .theme-toggle,
    .language-selector {
        display: none;
    }
    
    .main-content {
        margin-left: 0;
        margin-right: 0;
    }
}
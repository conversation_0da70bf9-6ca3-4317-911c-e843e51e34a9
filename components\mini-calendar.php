<div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white"><?php echo $lang['calendar']; ?></h3>
        <a href="calendar.php" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 text-sm">
            <?php echo $lang['openFullCalendar']; ?>
        </a>
    </div>

    <div class="mini-calendar">
        <div class="flex items-center justify-between mb-4">
            <button onclick="previousMonth()" class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <h4 class="text-sm font-medium text-gray-900 dark:text-white" id="currentMonth">
                <?php echo date('F Y'); ?>
            </h4>
            <button onclick="nextMonth()" class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>
        </div>

        <!-- Calendar Grid -->
        <div class="grid grid-cols-7 gap-1 text-center">
            <!-- Days of week header -->
            <?php
            $daysOfWeek = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
            foreach ($daysOfWeek as $day): ?>
                <div class="text-xs font-medium text-gray-500 dark:text-gray-400 py-2">
                    <?php echo substr($lang[$day], 0, 2); ?>
                </div>
            <?php endforeach; ?>

            <!-- Calendar days will be populated by JavaScript -->
            <div id="calendarDays" class="contents">
                <!-- Days will be generated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Today's quick stats -->
    <div class="mt-4 pt-4 border-t dark:border-gray-700">
        <div class="flex justify-between text-sm">
            <span class="text-gray-500 dark:text-gray-400"><?php echo $lang['today']; ?>:</span>
            <span class="text-gray-900 dark:text-white font-medium"><?php echo $stats['total']; ?> <?php echo $lang['tasks']; ?></span>
        </div>
    </div>
</div>
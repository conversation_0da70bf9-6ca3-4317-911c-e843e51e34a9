// AI Assistant JavaScript functionality
class AIAssistant {
    constructor() {
        this.chatMessages = document.getElementById('chatMessages');
        this.chatForm = document.getElementById('chatForm');
        this.chatInput = document.getElementById('chatInput');
        this.typingIndicator = document.getElementById('typingIndicator');
        this.dailyQuote = document.getElementById('dailyQuote');
        
        this.messageHistory = [];
        this.isTyping = false;
        
        this.init();
    }
    
    init() {
        // Initialize event listeners
        if (this.chatForm) {
            this.chatForm.addEventListener('submit', (e) => this.handleSubmit(e));
        }
        
        // Load daily quote
        this.loadDailyQuote();
        
        // Auto-focus chat input
        if (this.chatInput) {
            this.chatInput.focus();
        }
        
        // Add keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.chatInput) {
                this.chatInput.blur();
            }
        });
    }
    
    async handleSubmit(e) {
        e.preventDefault();
        
        const message = this.chatInput.value.trim();
        if (!message || this.isTyping) return;
        
        // Add user message
        this.addMessage(message, 'user');
        this.chatInput.value = '';
        
        // Show typing indicator
        this.showTyping();
        
        // Process AI response
        try {
            const response = await this.getAIResponse(message);
            this.hideTyping();
            this.addMessage(response, 'ai');
        } catch (error) {
            this.hideTyping();
            this.addMessage(window.translations.aiError || 'Sorry, I encountered an error. Please try again.', 'ai');
        }
    }
    
    addMessage(content, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'flex items-start gap-3';
        
        const time = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        
        if (sender === 'user') {
            messageDiv.className += ' justify-end';
            messageDiv.innerHTML = `
                <div class="bg-primary-500 text-white rounded-lg p-3 max-w-md">
                    <p>${this.escapeHtml(content)}</p>
                    <span class="text-xs text-primary-100 mt-2 block">${time}</span>
                </div>
                <div class="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                        ${window.userName.charAt(0).toUpperCase()}
                    </span>
                </div>
            `;
        } else {
            messageDiv.innerHTML = `
                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                </div>
                <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 max-w-md">
                    <div class="text-gray-900 dark:text-white">${content}</div>
                    <span class="text-xs text-gray-500 dark:text-gray-400 mt-2 block">${time}</span>
                </div>
            `;
        }
        
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
        
        // Store in history
        this.messageHistory.push({
            content: content,
            sender: sender,
            timestamp: new Date()
        });
    }
    
    showTyping() {
        this.isTyping = true;
        this.typingIndicator.classList.remove('hidden');
        this.scrollToBottom();
    }
    
    hideTyping() {
        this.isTyping = false;
        this.typingIndicator.classList.add('hidden');
    }
    
    scrollToBottom() {
        setTimeout(() => {
            this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
        }, 100);
    }
    
    async getAIResponse(message) {
        // Simulate AI processing delay
        await this.delay(1000 + Math.random() * 2000);
        
        const lowerMessage = message.toLowerCase();
        
        // Context-aware responses based on user data
        if (lowerMessage.includes('motivat') || lowerMessage.includes('inspire')) {
            return this.getMotivationalResponse();
        }
        
        if (lowerMessage.includes('productiv') || lowerMessage.includes('tip')) {
            return this.getProductivityTip();
        }
        
        if (lowerMessage.includes('task') && (lowerMessage.includes('analyz') || lowerMessage.includes('review'))) {
            return this.getTaskAnalysis();
        }
        
        if (lowerMessage.includes('suggest') && lowerMessage.includes('task')) {
            return this.getTaskSuggestions();
        }
        
        if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('hey')) {
            return this.getGreeting();
        }
        
        if (lowerMessage.includes('help')) {
            return this.getHelpResponse();
        }
        
        if (lowerMessage.includes('quote')) {
            return await this.getRandomQuote();
        }
        
        if (lowerMessage.includes('stat') || lowerMessage.includes('progress')) {
            return this.getStatsResponse();
        }
        
        // Default responses
        return this.getDefaultResponse();
    }
    
    getMotivationalResponse() {
        const motivationalMessages = [
            `🌟 ${window.translations.motivationalMsg1 || "You're doing great! Every completed task brings you closer to your goals."}`,
            `💪 ${window.translations.motivationalMsg2 || "Remember, progress is progress, no matter how small. Keep going!"}`,
            `🚀 ${window.translations.motivationalMsg3 || "Your dedication to productivity is inspiring. You've got this!"}`,
            `✨ ${window.translations.motivationalMsg4 || "Success is the sum of small efforts repeated day in and day out."}`,
            `🎯 ${window.translations.motivationalMsg5 || "Focus on progress, not perfection. You're building great habits!"}`
        ];
        
        return motivationalMessages[Math.floor(Math.random() * motivationalMessages.length)];
    }
    
    getProductivityTip() {
        const tips = [
            `📝 ${window.translations.tip1 || "Try the Pomodoro Technique: Work for 25 minutes, then take a 5-minute break."}`,
            `🎯 ${window.translations.tip2 || "Prioritize your tasks using the Eisenhower Matrix: Important vs Urgent."}`,
            `🌅 ${window.translations.tip3 || "Start your day by completing your most challenging task first."}`,
            `📱 ${window.translations.tip4 || "Minimize distractions by turning off non-essential notifications."}`,
            `✅ ${window.translations.tip5 || "Break large tasks into smaller, manageable subtasks."}`,
            `🔄 ${window.translations.tip6 || "Use the 2-minute rule: If it takes less than 2 minutes, do it now."}`,
            `📊 ${window.translations.tip7 || "Review your tasks weekly to stay aligned with your goals."}`
        ];
        
        return tips[Math.floor(Math.random() * tips.length)];
    }
    
    getTaskAnalysis() {
        const stats = window.userStats;
        const completionRate = stats.total_tasks > 0 ? Math.round((stats.completed_tasks / stats.total_tasks) * 100) : 0;
        
        let analysis = `📊 Here's your task analysis:\n\n`;
        analysis += `• Total tasks: ${stats.total_tasks}\n`;
        analysis += `• Completed: ${stats.completed_tasks} (${completionRate}%)\n`;
        analysis += `• Pending: ${stats.pending_tasks}\n`;
        analysis += `• Today's tasks: ${stats.today_tasks}\n`;
        
        if (stats.overdue_tasks > 0) {
            analysis += `• ⚠️ Overdue: ${stats.overdue_tasks}\n\n`;
            analysis += `💡 Suggestion: Focus on completing overdue tasks first to get back on track.`;
        } else if (completionRate >= 80) {
            analysis += `\n🎉 Excellent! You have a ${completionRate}% completion rate. Keep up the great work!`;
        } else if (completionRate >= 60) {
            analysis += `\n👍 Good progress! You're at ${completionRate}% completion. Consider breaking down larger tasks.`;
        } else {
            analysis += `\n💪 You're at ${completionRate}% completion. Try focusing on 2-3 important tasks today.`;
        }
        
        return analysis;
    }
    
    getTaskSuggestions() {
        const suggestions = [
            `📚 ${window.translations.suggestion1 || "Review and organize your workspace"}`,
            `💡 ${window.translations.suggestion2 || "Plan tomorrow's priorities"}`,
            `🏃 ${window.translations.suggestion3 || "Take a 10-minute walk or exercise break"}`,
            `📖 ${window.translations.suggestion4 || "Read for 15 minutes on a topic you're interested in"}`,
            `🧘 ${window.translations.suggestion5 || "Practice 5 minutes of mindfulness or meditation"}`,
            `📞 ${window.translations.suggestion6 || "Reach out to a colleague or friend"}`,
            `🎯 ${window.translations.suggestion7 || "Set a specific goal for next week"}`,
            `📝 ${window.translations.suggestion8 || "Write down three things you're grateful for"}`
        ];
        
        const randomSuggestions = suggestions.sort(() => 0.5 - Math.random()).slice(0, 3);
        
        return `💡 Here are some task suggestions for you:\n\n${randomSuggestions.join('\n')}`;
    }
    
    getGreeting() {
        const greetings = [
            `👋 ${window.translations.greeting1 || `Hello ${window.userName}! How can I help you be more productive today?`}`,
            `🌟 ${window.translations.greeting2 || `Hi there! Ready to tackle your tasks?`}`,
            `💪 ${window.translations.greeting3 || `Hey ${window.userName}! Let's make today productive!`}`
        ];
        
        return greetings[Math.floor(Math.random() * greetings.length)];
    }
    
    getHelpResponse() {
        return `🤖 ${window.translations.helpResponse || "I can help you with:"}\n\n` +
               `• ${window.translations.helpItem1 || "Motivational messages and quotes"}\n` +
               `• ${window.translations.helpItem2 || "Productivity tips and techniques"}\n` +
               `• ${window.translations.helpItem3 || "Task analysis and insights"}\n` +
               `• ${window.translations.helpItem4 || "Task suggestions and ideas"}\n` +
               `• ${window.translations.helpItem5 || "Progress tracking and statistics"}\n\n` +
               `${window.translations.helpFooter || "Just ask me anything about productivity!"}`;
    }
    
    getStatsResponse() {
        const stats = window.userStats;
        const completionRate = stats.total_tasks > 0 ? Math.round((stats.completed_tasks / stats.total_tasks) * 100) : 0;
        
        return `📈 Your current statistics:\n\n` +
               `• Completion rate: ${completionRate}%\n` +
               `• Tasks completed: ${stats.completed_tasks}\n` +
               `• Tasks pending: ${stats.pending_tasks}\n` +
               `• Today's tasks: ${stats.today_tasks}\n` +
               (stats.overdue_tasks > 0 ? `• Overdue tasks: ${stats.overdue_tasks}\n` : '') +
               `\n${completionRate >= 70 ? '🎉 Great job!' : '💪 Keep pushing forward!'}`;
    }
    
    getDefaultResponse() {
        const responses = [
            `🤔 ${window.translations.defaultResponse1 || "That's interesting! Can you tell me more about what you'd like help with?"}`,
            `💭 ${window.translations.defaultResponse2 || "I'm here to help with your productivity. Try asking about tasks, motivation, or tips!"}`,
            `🎯 ${window.translations.defaultResponse3 || "I can help you with task management, productivity tips, or motivation. What would you like to know?"}`,
            `✨ ${window.translations.defaultResponse4 || "Feel free to ask me about your tasks, productivity strategies, or if you need some motivation!"}`
        ];
        
        return responses[Math.floor(Math.random() * responses.length)];
    }
    
    async getRandomQuote() {
        try {
            const response = await fetch('api/get-quote.php');
            const data = await response.json();
            
            if (data.success && data.quote) {
                return `💬 "${data.quote.text}"\n\n— ${data.quote.author}`;
            }
        } catch (error) {
            console.error('Error fetching quote:', error);
        }
        
        // Fallback quotes
        const fallbackQuotes = [
            `💬 "${window.translations.fallbackQuote1 || 'The way to get started is to quit talking and begin doing.'}"\n\n— Walt Disney`,
            `💬 "${window.translations.fallbackQuote2 || 'Success is not final, failure is not fatal: it is the courage to continue that counts.'}"\n\n— Winston Churchill`,
            `💬 "${window.translations.fallbackQuote3 || 'The future depends on what you do today.'}"\n\n— Mahatma Gandhi`
        ];
        
        return fallbackQuotes[Math.floor(Math.random() * fallbackQuotes.length)];
    }
    
    async loadDailyQuote() {
        if (!this.dailyQuote) return;
        
        try {
            const response = await fetch('api/get-quote.php');
            const data = await response.json();
            
            if (data.success && data.quote) {
                this.dailyQuote.innerHTML = `
                    <blockquote class="text-gray-700 dark:text-gray-300 italic mb-3">
                        "${data.quote.text}"
                    </blockquote>
                    <cite class="text-sm text-gray-500 dark:text-gray-400">
                        — ${data.quote.author}
                    </cite>
                `;
            } else {
                throw new Error('No quote received');
            }
        } catch (error) {
            console.error('Error loading daily quote:', error);
            this.dailyQuote.innerHTML = `
                <blockquote class="text-gray-700 dark:text-gray-300 italic mb-3">
                    "${window.translations.fallbackQuote1 || 'The way to get started is to quit talking and begin doing.'}"
                </blockquote>
                <cite class="text-sm text-gray-500 dark:text-gray-400">
                    — Walt Disney
                </cite>
            `;
        }
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Global function for quick actions
function askAI(question) {
    if (window.aiAssistant && window.aiAssistant.chatInput) {
        window.aiAssistant.chatInput.value = question;
        window.aiAssistant.chatInput.focus();
        window.aiAssistant.handleSubmit(new Event('submit'));
    }
}

// Initialize AI Assistant when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.aiAssistant = new AIAssistant();
});

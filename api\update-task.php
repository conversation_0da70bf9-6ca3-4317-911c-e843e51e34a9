<?php
session_start();
require_once '../config/db.php';
require_once '../includes/auth.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Task ID is required']);
    exit();
}

$taskId = (int)$input['id'];
$updateData = [];

// Build update data from input
if (isset($input['title']) && !empty(trim($input['title']))) {
    $updateData['title'] = trim($input['title']);
}

if (isset($input['description'])) {
    $updateData['description'] = trim($input['description']);
}

if (isset($input['priority']) && in_array($input['priority'], ['low', 'medium', 'high'])) {
    $updateData['priority'] = $input['priority'];
}

if (isset($input['status']) && in_array($input['status'], ['pending', 'in_progress', 'completed'])) {
    $updateData['status'] = $input['status'];
    
    // Set completed_at timestamp if completing task
    if ($input['status'] === 'completed') {
        $updateData['completed_at'] = date('Y-m-d H:i:s');
    } else {
        $updateData['completed_at'] = null;
    }
}

if (isset($input['due_date']) && !empty($input['due_date'])) {
    $updateData['due_date'] = $input['due_date'] . ' 23:59:59';
}

if (empty($updateData)) {
    http_response_code(400);
    echo json_encode(['error' => 'No valid update data provided']);
    exit();
}

try {
    $result = updateTask($taskId, $_SESSION['user_id'], $updateData);
    
    if ($result) {
        $message = isset($input['status']) && $input['status'] === 'completed' 
            ? 'Task completed successfully' 
            : 'Task updated successfully';
            
        echo json_encode([
            'success' => true,
            'message' => $message
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update task']);
    }
} catch (Exception $e) {
    error_log("Update task error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
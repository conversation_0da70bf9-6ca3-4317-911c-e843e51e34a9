<!-- Task Modal -->
<div id="taskModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full mx-4 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 id="modalTitle" class="text-lg font-semibold text-gray-900 dark:text-white">
                <?php echo $lang['newTask']; ?>
            </h3>
            <button onclick="closeTaskModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form id="taskForm" class="space-y-4">
            <input type="hidden" id="taskId" name="taskId">
            
            <div>
                <label for="taskTitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <?php echo $lang['taskTitle']; ?>
                </label>
                <input type="text" id="taskTitle" name="title" required
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                       placeholder="<?php echo $lang['taskPlaceholder']; ?>">
            </div>

            <div>
                <label for="taskDescription" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <?php echo $lang['taskDescription']; ?>
                </label>
                <textarea id="taskDescription" name="description" rows="3"
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          placeholder="<?php echo $lang['descriptionPlaceholder']; ?>"></textarea>
            </div>

            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label for="taskPriority" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <?php echo $lang['priority']; ?>
                    </label>
                    <select id="taskPriority" name="priority"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        <option value="low"><?php echo $lang['low']; ?></option>
                        <option value="medium" selected><?php echo $lang['medium']; ?></option>
                        <option value="high"><?php echo $lang['high']; ?></option>
                    </select>
                </div>

                <div>
                    <label for="taskDueDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <?php echo $lang['dueDate']; ?>
                    </label>
                    <input type="date" id="taskDueDate" name="due_date" 
                           value="<?php echo date('Y-m-d'); ?>"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                </div>
            </div>

            <div class="flex gap-3 pt-4">
                <button type="button" onclick="closeTaskModal()" 
                        class="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <?php echo $lang['cancel']; ?>
                </button>
                <button type="submit" id="taskSubmitBtn"
                        class="flex-1 bg-primary-500 hover:bg-primary-600 text-white py-2 px-4 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                    <?php echo $lang['save']; ?>
                </button>
            </div>
        </form>
    </div>
</div>
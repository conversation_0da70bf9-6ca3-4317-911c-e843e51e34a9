<?php
session_start();
require_once 'config/db.php';
require_once 'includes/auth.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// Get user data and preferences
$user = getUserById($_SESSION['user_id']);
$preferences = getUserPreferences($_SESSION['user_id']);
$theme = $preferences['theme'] ?? 'light';
$language = $preferences['language'] ?? 'ar';

// Include language file
require_once "lang/{$language}.php";
?>

<!DOCTYPE html>
<html lang="<?php echo $language; ?>" dir="<?php echo $language === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $lang['about'] ?? 'About'; ?> - <?php echo $lang['appTitle']; ?></title>
    <meta name="description" content="<?php echo $lang['appDescription']; ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a'
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="<?php echo $theme === 'dark' ? 'dark' : ''; ?> bg-gray-50 dark:bg-gray-900">
    <!-- Language Selector -->
    <?php include 'components/language-selector.php'; ?>
    
    <!-- Theme Toggle -->
    <?php include 'components/theme-toggle.php'; ?>
    
    <!-- Sidebar Navigation -->
    <?php include 'components/sidebar-nav.php'; ?>
    
    <div class="<?php echo $language === 'ar' ? 'mr-64' : 'ml-64'; ?> min-h-screen transition-all duration-300">
        <!-- Header -->
        <header class="bg-white dark:bg-gray-800 shadow-sm border-b dark:border-gray-700 px-6 py-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                        <?php echo $lang['about'] ?? 'About'; ?>
                    </h1>
                    <p class="text-gray-500 dark:text-gray-400">
                        <?php echo $lang['aboutDescription'] ?? 'Learn more about LifePilot and get help'; ?>
                    </p>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="p-6">
            <!-- App Information -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-8 mb-8">
                <div class="text-center mb-8">
                    <div class="w-20 h-20 bg-primary-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                    </div>
                    <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                        <?php echo $lang['appTitle']; ?>
                    </h2>
                    <p class="text-lg text-gray-600 dark:text-gray-400 mb-4">
                        <?php echo $lang['appDescription']; ?>
                    </p>
                    <div class="inline-flex items-center px-4 py-2 bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 rounded-full">
                        <span class="text-sm font-medium">Version 2.0</span>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                            <?php echo $lang['fastEfficient'] ?? 'Fast & Efficient'; ?>
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400">
                            <?php echo $lang['fastEfficientDesc'] ?? 'Streamlined interface for quick task management and productivity tracking'; ?>
                        </p>
                    </div>

                    <div class="text-center">
                        <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                            <?php echo $lang['secure'] ?? 'Secure'; ?>
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400">
                            <?php echo $lang['secureDesc'] ?? 'Your data is protected with industry-standard security measures'; ?>
                        </p>
                    </div>

                    <div class="text-center">
                        <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                            <?php echo $lang['multilingual'] ?? 'Multilingual'; ?>
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400">
                            <?php echo $lang['multilingualDesc'] ?? 'Available in Arabic, English, and French languages'; ?>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Features -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                    <?php echo $lang['features'] ?? 'Features'; ?>
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="flex items-start gap-4">
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 dark:text-white mb-1">
                                <?php echo $lang['taskManagement'] ?? 'Task Management'; ?>
                            </h3>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">
                                <?php echo $lang['taskManagementDesc'] ?? 'Create, edit, and organize tasks with priorities and due dates'; ?>
                            </p>
                        </div>
                    </div>

                    <div class="flex items-start gap-4">
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 dark:text-white mb-1">
                                <?php echo $lang['analytics'] ?? 'Analytics'; ?>
                            </h3>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">
                                <?php echo $lang['analyticsDesc'] ?? 'Track your productivity with detailed reports and charts'; ?>
                            </p>
                        </div>
                    </div>

                    <div class="flex items-start gap-4">
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 dark:text-white mb-1">
                                <?php echo $lang['calendar'] ?? 'Calendar'; ?>
                            </h3>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">
                                <?php echo $lang['calendarDesc'] ?? 'Visual calendar view with task integration and scheduling'; ?>
                            </p>
                        </div>
                    </div>

                    <div class="flex items-start gap-4">
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 dark:text-white mb-1">
                                <?php echo $lang['darkMode'] ?? 'Dark Mode'; ?>
                            </h3>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">
                                <?php echo $lang['darkModeDesc'] ?? 'Switch between light and dark themes for comfortable viewing'; ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Help & Support -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                    <?php echo $lang['helpSupport'] ?? 'Help & Support'; ?>
                </h2>
                
                <div class="space-y-6">
                    <!-- FAQ -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            <?php echo $lang['faq'] ?? 'Frequently Asked Questions'; ?>
                        </h3>
                        <div class="space-y-4">
                            <div class="border-l-4 border-primary-500 pl-4">
                                <h4 class="font-medium text-gray-900 dark:text-white mb-1">
                                    <?php echo $lang['howToCreateTask'] ?? 'How do I create a new task?'; ?>
                                </h4>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">
                                    <?php echo $lang['howToCreateTaskAnswer'] ?? 'Click the "New Task" button in the header or use the keyboard shortcut Ctrl+K to open the task creation modal.'; ?>
                                </p>
                            </div>

                            <div class="border-l-4 border-primary-500 pl-4">
                                <h4 class="font-medium text-gray-900 dark:text-white mb-1">
                                    <?php echo $lang['howToChangeLanguage'] ?? 'How do I change the language?'; ?>
                                </h4>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">
                                    <?php echo $lang['howToChangeLanguageAnswer'] ?? 'Use the language selector in the top corner or go to your Profile settings to change the default language.'; ?>
                                </p>
                            </div>

                            <div class="border-l-4 border-primary-500 pl-4">
                                <h4 class="font-medium text-gray-900 dark:text-white mb-1">
                                    <?php echo $lang['howToViewReports'] ?? 'How do I view my productivity reports?'; ?>
                                </h4>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">
                                    <?php echo $lang['howToViewReportsAnswer'] ?? 'Navigate to the Reports page from the sidebar to view detailed analytics and charts of your productivity.'; ?>
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Keyboard Shortcuts -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            <?php echo $lang['keyboardShortcuts'] ?? 'Keyboard Shortcuts'; ?>
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                <span class="text-gray-900 dark:text-white"><?php echo $lang['newTask']; ?></span>
                                <kbd class="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded text-sm">Ctrl + K</kbd>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                <span class="text-gray-900 dark:text-white"><?php echo $lang['closeModal'] ?? 'Close Modal'; ?></span>
                                <kbd class="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded text-sm">Esc</kbd>
                            </div>
                        </div>
                    </div>

                    <!-- Contact -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            <?php echo $lang['contact'] ?? 'Contact'; ?>
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                            <?php echo $lang['contactDesc'] ?? 'Need help or have suggestions? We\'d love to hear from you!'; ?>
                        </p>
                        <div class="flex flex-wrap gap-4">
                            <a href="mailto:<EMAIL>" class="inline-flex items-center gap-2 px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg transition-colors">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                <?php echo $lang['emailSupport'] ?? 'Email Support'; ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Technical Information -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-8">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                    <?php echo $lang['technicalInfo'] ?? 'Technical Information'; ?>
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-semibold text-gray-900 dark:text-white mb-3">
                            <?php echo $lang['systemRequirements'] ?? 'System Requirements'; ?>
                        </h3>
                        <ul class="space-y-2 text-gray-600 dark:text-gray-400">
                            <li>• <?php echo $lang['modernBrowser'] ?? 'Modern web browser (Chrome, Firefox, Safari, Edge)'; ?></li>
                            <li>• <?php echo $lang['javascriptEnabled'] ?? 'JavaScript enabled'; ?></li>
                            <li>• <?php echo $lang['internetConnection'] ?? 'Internet connection'; ?></li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900 dark:text-white mb-3">
                            <?php echo $lang['builtWith'] ?? 'Built With'; ?>
                        </h3>
                        <ul class="space-y-2 text-gray-600 dark:text-gray-400">
                            <li>• PHP 7.4+</li>
                            <li>• MySQL 5.7+</li>
                            <li>• Tailwind CSS</li>
                            <li>• Chart.js</li>
                        </ul>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script>
        // Pass PHP translations to JavaScript
        window.translations = <?php echo json_encode($lang); ?>;
    </script>
    <script src="assets/js/theme.js"></script>
    <script src="assets/js/language.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>

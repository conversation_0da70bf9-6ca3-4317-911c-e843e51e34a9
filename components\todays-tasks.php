<div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white"><?php echo $lang['todayTasks']; ?></h3>
        <a href="tasks.php" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 text-sm">
            <?php echo $lang['viewAll']; ?>
        </a>
    </div>

    <?php if (empty($todayTasks)): ?>
        <div class="text-center py-8">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
            <p class="text-gray-500 dark:text-gray-400"><?php echo $lang['noTasksToday']; ?></p>
        </div>
    <?php else: ?>
        <div class="space-y-3">
            <?php foreach ($todayTasks as $task): ?>
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center flex-1">
                        <button onclick="toggleTaskStatus(<?php echo $task['id']; ?>, '<?php echo $task['status']; ?>')" 
                                class="<?php echo $language === 'ar' ? 'ml-3' : 'mr-3'; ?> w-5 h-5 rounded border-2 flex items-center justify-center transition-colors
                                       <?php echo $task['status'] === 'completed' ? 'bg-green-500 border-green-500' : 'border-gray-300 hover:border-green-500'; ?>">
                            <?php if ($task['status'] === 'completed'): ?>
                                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            <?php endif; ?>
                        </button>
                        
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white <?php echo $task['status'] === 'completed' ? 'line-through text-gray-500' : ''; ?>">
                                <?php echo htmlspecialchars($task['title']); ?>
                            </h4>
                            <?php if ($task['description']): ?>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                    <?php echo htmlspecialchars($task['description']); ?>
                                </p>
                            <?php endif; ?>
                        </div>
                        
                        <div class="flex items-center gap-2">
                            <span class="px-2 py-1 text-xs rounded-full
                                       <?php 
                                       switch($task['priority']) {
                                           case 'high': echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'; break;
                                           case 'medium': echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'; break;
                                           case 'low': echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'; break;
                                       }
                                       ?>">
                                <?php echo $lang[$task['priority']]; ?>
                            </span>
                            
                            <div class="flex gap-1">
                                <button onclick="editTask(<?php echo $task['id']; ?>)" class="text-gray-400 hover:text-blue-500 transition-colors">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </button>
                                <button onclick="deleteTask(<?php echo $task['id']; ?>)" class="text-gray-400 hover:text-red-500 transition-colors">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>
-- LifePilot MySQL Database Setup Script
-- Run this script in phpMyAdmin to create the database and tables

-- Create database
CREATE DATABASE IF NOT EXISTS lifepilot_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use the database
USE lifepilot_db;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name VA<PERSON><PERSON><PERSON>(100),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    profile_image_url TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User preferences table
CREATE TABLE IF NOT EXISTS user_preferences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    theme VARCHAR(20) DEFAULT 'light',
    language VARCHAR(10) DEFAULT 'ar',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tasks table
CREATE TABLE IF NOT EXISTS tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    status ENUM('pending', 'in_progress', 'completed') DEFAULT 'pending',
    due_date DATETIME,
    completed_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_due_date (user_id, due_date),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Quotes table for AI assistant
CREATE TABLE IF NOT EXISTS quotes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    text TEXT NOT NULL,
    author VARCHAR(255),
    language VARCHAR(10) DEFAULT 'ar',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_language (language)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default quotes
INSERT INTO quotes (text, author, language) VALUES
-- Arabic quotes
('النجاح هو المشي من فشل إلى فشل بلا فقدان للحماس.', 'ونستون تشرشل', 'ar'),
('الطريق إلى النجاح يبدأ بخطوة واحدة.', 'لاو تزو', 'ar'),
('لا تؤجل عمل اليوم إلى الغد، فالغد قد لا يأتي أبداً.', 'بنجامين فرانكلين', 'ar'),
('الإنتاجية ليست في كثرة العمل، بل في إنجاز الأهم أولاً.', 'ستيفن كوفي', 'ar'),

-- English quotes
('Success is walking from failure to failure with no loss of enthusiasm.', 'Winston Churchill', 'en'),
('The way to get started is to quit talking and begin doing.', 'Walt Disney', 'en'),
('Don\'t put off until tomorrow what you can do today.', 'Benjamin Franklin', 'en'),
('Productivity is not about doing more, but about doing what matters most.', 'Stephen Covey', 'en'),

-- French quotes
('Le succès c\'est marcher d\'échec en échec tout en gardant son enthousiasme.', 'Winston Churchill', 'fr'),
('La façon de commencer est d\'arrêter de parler et de commencer à faire.', 'Walt Disney', 'fr'),
('Ne remets pas à demain ce que tu peux faire aujourd\'hui.', 'Benjamin Franklin', 'fr'),
('La productivité ne consiste pas à faire plus, mais à faire ce qui compte le plus.', 'Stephen Covey', 'fr');

-- Create a sample admin user (password: admin123)
-- You should change this password after first login
INSERT INTO users (first_name, last_name, email, password_hash) VALUES
('Admin', 'User', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi');

-- Create default preferences for admin user
INSERT INTO user_preferences (user_id, theme, language) VALUES
(1, 'light', 'ar');

-- AI Conversations table for storing chat history
CREATE TABLE IF NOT EXISTS ai_conversations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    message TEXT NOT NULL,
    response TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_created (user_id, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Productivity tips table
CREATE TABLE IF NOT EXISTS productivity_tips (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    category VARCHAR(100),
    language VARCHAR(10) DEFAULT 'ar',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_language_category (language, category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert productivity tips
INSERT INTO productivity_tips (title, content, category, language) VALUES
-- English tips
('Pomodoro Technique', 'Work for 25 minutes, then take a 5-minute break. After 4 cycles, take a longer 15-30 minute break.', 'time_management', 'en'),
('Eisenhower Matrix', 'Categorize tasks by importance and urgency: Do (urgent+important), Decide (important), Delegate (urgent), Delete (neither).', 'prioritization', 'en'),
('Two-Minute Rule', 'If a task takes less than 2 minutes to complete, do it immediately instead of adding it to your task list.', 'efficiency', 'en'),
('Time Blocking', 'Schedule specific time blocks for different types of work. This helps maintain focus and prevents multitasking.', 'time_management', 'en'),
('Eat the Frog', 'Complete your most challenging or important task first thing in the morning when your energy is highest.', 'motivation', 'en'),

-- Arabic tips
('تقنية البومودورو', 'اعمل لمدة 25 دقيقة، ثم خذ استراحة 5 دقائق. بعد 4 دورات، خذ استراحة أطول من 15-30 دقيقة.', 'time_management', 'ar'),
('مصفوفة أيزنهاور', 'صنف المهام حسب الأهمية والإلحاح: افعل (عاجل+مهم)، قرر (مهم)، فوض (عاجل)، احذف (لا شيء).', 'prioritization', 'ar'),
('قاعدة الدقيقتين', 'إذا كانت المهمة تستغرق أقل من دقيقتين، افعلها فوراً بدلاً من إضافتها لقائمة المهام.', 'efficiency', 'ar'),
('تخصيص الوقت', 'جدول كتل زمنية محددة لأنواع مختلفة من العمل. هذا يساعد في الحفاظ على التركيز ومنع تعدد المهام.', 'time_management', 'ar'),
('ابدأ بالأصعب', 'أكمل مهمتك الأكثر تحدياً أو أهمية أول شيء في الصباح عندما تكون طاقتك في أعلى مستوياتها.', 'motivation', 'ar');

-- Create some sample tasks for the admin user
INSERT INTO tasks (user_id, title, description, priority, status, due_date) VALUES
(1, 'Welcome to LifePilot', 'This is your first task. You can edit or delete it.', 'medium', 'pending', CURDATE()),
(1, 'Set up your profile', 'Update your profile information and preferences.', 'high', 'pending', CURDATE()),
(1, 'Explore the features', 'Take a tour of all the productivity features available.', 'low', 'pending', DATE_ADD(CURDATE(), INTERVAL 1 DAY)),
(1, 'Try the AI Assistant', 'Chat with the AI assistant to get productivity tips and motivation.', 'medium', 'pending', CURDATE());

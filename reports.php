<?php
session_start();
require_once 'config/db.php';
require_once 'includes/auth.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// Get user data and preferences
$user = getUserById($_SESSION['user_id']);
$preferences = getUserPreferences($_SESSION['user_id']);
$theme = $preferences['theme'] ?? 'light';
$language = $preferences['language'] ?? 'ar';

// Include language file
require_once "lang/{$language}.php";

// Get date range for reports
$date_range = $_GET['range'] ?? '30';
$valid_ranges = ['7', '30', '90', '365'];
if (!in_array($date_range, $valid_ranges)) {
    $date_range = '30';
}

try {
    $pdo = getDB();
    
    // Overall statistics
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_tasks,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_tasks,
            COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_tasks,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_tasks,
            COUNT(CASE WHEN due_date < NOW() AND status != 'completed' THEN 1 END) as overdue_tasks
        FROM tasks 
        WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
    ");
    $stmt->execute([$_SESSION['user_id'], $date_range]);
    $overall_stats = $stmt->fetch();
    
    // Daily completion data for chart
    $stmt = $pdo->prepare("
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as created,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed
        FROM tasks 
        WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY DATE(created_at)
        ORDER BY DATE(created_at)
    ");
    $stmt->execute([$_SESSION['user_id'], $date_range]);
    $daily_data = $stmt->fetchAll();
    
    // Priority distribution
    $stmt = $pdo->prepare("
        SELECT 
            priority,
            COUNT(*) as count,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed
        FROM tasks 
        WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY priority
    ");
    $stmt->execute([$_SESSION['user_id'], $date_range]);
    $priority_data = $stmt->fetchAll();
    
    // Weekly productivity
    $stmt = $pdo->prepare("
        SELECT 
            DAYNAME(created_at) as day_name,
            DAYOFWEEK(created_at) as day_num,
            COUNT(*) as tasks_created,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as tasks_completed
        FROM tasks 
        WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY DAYOFWEEK(created_at), DAYNAME(created_at)
        ORDER BY DAYOFWEEK(created_at)
    ");
    $stmt->execute([$_SESSION['user_id'], $date_range]);
    $weekly_data = $stmt->fetchAll();
    
    // Recent activity
    $stmt = $pdo->prepare("
        SELECT title, status, priority, created_at, completed_at
        FROM tasks 
        WHERE user_id = ? 
        ORDER BY 
            CASE WHEN completed_at IS NOT NULL THEN completed_at ELSE created_at END DESC
        LIMIT 10
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $recent_activity = $stmt->fetchAll();
    
} catch (PDOException $e) {
    error_log("Reports query error: " . $e->getMessage());
    $overall_stats = ['total_tasks' => 0, 'completed_tasks' => 0, 'in_progress_tasks' => 0, 'pending_tasks' => 0, 'overdue_tasks' => 0];
    $daily_data = [];
    $priority_data = [];
    $weekly_data = [];
    $recent_activity = [];
}

// Calculate completion rate
$completion_rate = $overall_stats['total_tasks'] > 0 ? 
    round(($overall_stats['completed_tasks'] / $overall_stats['total_tasks']) * 100) : 0;
?>

<!DOCTYPE html>
<html lang="<?php echo $language; ?>" dir="<?php echo $language === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $lang['reports'] ?? 'Reports'; ?> - <?php echo $lang['appTitle']; ?></title>
    <meta name="description" content="<?php echo $lang['appDescription']; ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a'
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="<?php echo $theme === 'dark' ? 'dark' : ''; ?> bg-gray-50 dark:bg-gray-900">
    <!-- Language Selector -->
    <?php include 'components/language-selector.php'; ?>
    
    <!-- Theme Toggle -->
    <?php include 'components/theme-toggle.php'; ?>
    
    <!-- Sidebar Navigation -->
    <?php include 'components/sidebar-nav.php'; ?>
    
    <div class="<?php echo $language === 'ar' ? 'mr-64' : 'ml-64'; ?> min-h-screen transition-all duration-300">
        <!-- Header -->
        <header class="bg-white dark:bg-gray-800 shadow-sm border-b dark:border-gray-700 px-6 py-4">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                        <?php echo $lang['reports'] ?? 'Reports'; ?>
                    </h1>
                    <p class="text-gray-500 dark:text-gray-400">
                        <?php echo $lang['productivityAnalytics'] ?? 'Productivity analytics and insights'; ?>
                    </p>
                </div>
                <div class="flex gap-3">
                    <select onchange="changeTimeRange(this.value)" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        <option value="7" <?php echo $date_range === '7' ? 'selected' : ''; ?>><?php echo $lang['last7Days'] ?? 'Last 7 Days'; ?></option>
                        <option value="30" <?php echo $date_range === '30' ? 'selected' : ''; ?>><?php echo $lang['last30Days'] ?? 'Last 30 Days'; ?></option>
                        <option value="90" <?php echo $date_range === '90' ? 'selected' : ''; ?>><?php echo $lang['last90Days'] ?? 'Last 90 Days'; ?></option>
                        <option value="365" <?php echo $date_range === '365' ? 'selected' : ''; ?>><?php echo $lang['lastYear'] ?? 'Last Year'; ?></option>
                    </select>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="p-6">
            <!-- Overview Stats -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo $lang['totalTasks'] ?? 'Total Tasks'; ?></p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?php echo $overall_stats['total_tasks']; ?></p>
                        </div>
                        <div class="bg-blue-100 dark:bg-blue-900 p-3 rounded-lg">
                            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo $lang['completed']; ?></p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?php echo $overall_stats['completed_tasks']; ?></p>
                        </div>
                        <div class="bg-green-100 dark:bg-green-900 p-3 rounded-lg">
                            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo $lang['inProgress']; ?></p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?php echo $overall_stats['in_progress_tasks']; ?></p>
                        </div>
                        <div class="bg-yellow-100 dark:bg-yellow-900 p-3 rounded-lg">
                            <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo $lang['overdue'] ?? 'Overdue'; ?></p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?php echo $overall_stats['overdue_tasks']; ?></p>
                        </div>
                        <div class="bg-red-100 dark:bg-red-900 p-3 rounded-lg">
                            <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo $lang['completionRate']; ?></p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?php echo $completion_rate; ?>%</p>
                        </div>
                        <div class="bg-purple-100 dark:bg-purple-900 p-3 rounded-lg">
                            <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Daily Activity Chart -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        <?php echo $lang['dailyActivity'] ?? 'Daily Activity'; ?>
                    </h3>
                    <canvas id="dailyChart" width="400" height="200"></canvas>
                </div>

                <!-- Priority Distribution -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        <?php echo $lang['priorityDistribution'] ?? 'Priority Distribution'; ?>
                    </h3>
                    <canvas id="priorityChart" width="400" height="200"></canvas>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Weekly Productivity -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        <?php echo $lang['weeklyProductivity'] ?? 'Weekly Productivity'; ?>
                    </h3>
                    <canvas id="weeklyChart" width="400" height="200"></canvas>
                </div>

                <!-- Recent Activity -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        <?php echo $lang['recentActivity'] ?? 'Recent Activity'; ?>
                    </h3>
                    <div class="space-y-3">
                        <?php if (empty($recent_activity)): ?>
                            <p class="text-gray-500 dark:text-gray-400 text-center py-8">
                                <?php echo $lang['noRecentActivity'] ?? 'No recent activity'; ?>
                            </p>
                        <?php else: ?>
                            <?php foreach ($recent_activity as $activity): ?>
                                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div class="flex-1">
                                        <h4 class="font-medium text-gray-900 dark:text-white <?php echo $activity['status'] === 'completed' ? 'line-through text-gray-500' : ''; ?>">
                                            <?php echo htmlspecialchars($activity['title']); ?>
                                        </h4>
                                        <div class="flex items-center gap-2 mt-1">
                                            <span class="px-2 py-1 text-xs rounded-full
                                                       <?php 
                                                       switch($activity['priority']) {
                                                           case 'high': echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'; break;
                                                           case 'medium': echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'; break;
                                                           case 'low': echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'; break;
                                                       }
                                                       ?>">
                                                <?php echo $lang[$activity['priority']]; ?>
                                            </span>
                                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                                <?php 
                                                $date = $activity['completed_at'] ?: $activity['created_at'];
                                                echo date('M j, H:i', strtotime($date)); 
                                                ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="w-3 h-3 rounded-full
                                               <?php 
                                               switch($activity['status']) {
                                                   case 'completed': echo 'bg-green-500'; break;
                                                   case 'in_progress': echo 'bg-blue-500'; break;
                                                   case 'pending': echo 'bg-gray-400'; break;
                                               }
                                               ?>">
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script>
        // Pass PHP data to JavaScript
        window.translations = <?php echo json_encode($lang); ?>;
        window.dailyData = <?php echo json_encode($daily_data); ?>;
        window.priorityData = <?php echo json_encode($priority_data); ?>;
        window.weeklyData = <?php echo json_encode($weekly_data); ?>;
        window.isDarkMode = <?php echo $theme === 'dark' ? 'true' : 'false'; ?>;
        
        function changeTimeRange(range) {
            window.location.href = '?range=' + range;
        }
        
        // Chart colors based on theme
        const colors = {
            primary: window.isDarkMode ? '#60a5fa' : '#3b82f6',
            success: window.isDarkMode ? '#34d399' : '#10b981',
            warning: window.isDarkMode ? '#fbbf24' : '#f59e0b',
            danger: window.isDarkMode ? '#f87171' : '#ef4444',
            text: window.isDarkMode ? '#f3f4f6' : '#374151',
            grid: window.isDarkMode ? '#374151' : '#e5e7eb'
        };
        
        // Daily Activity Chart
        const dailyCtx = document.getElementById('dailyChart').getContext('2d');
        new Chart(dailyCtx, {
            type: 'line',
            data: {
                labels: window.dailyData.map(d => new Date(d.date).toLocaleDateString()),
                datasets: [{
                    label: window.translations.tasksCreated || 'Tasks Created',
                    data: window.dailyData.map(d => d.created),
                    borderColor: colors.primary,
                    backgroundColor: colors.primary + '20',
                    tension: 0.4
                }, {
                    label: window.translations.tasksCompleted || 'Tasks Completed',
                    data: window.dailyData.map(d => d.completed),
                    borderColor: colors.success,
                    backgroundColor: colors.success + '20',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        labels: {
                            color: colors.text
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: { color: colors.text },
                        grid: { color: colors.grid }
                    },
                    y: {
                        ticks: { color: colors.text },
                        grid: { color: colors.grid }
                    }
                }
            }
        });
        
        // Priority Distribution Chart
        const priorityCtx = document.getElementById('priorityChart').getContext('2d');
        new Chart(priorityCtx, {
            type: 'doughnut',
            data: {
                labels: window.priorityData.map(d => window.translations[d.priority] || d.priority),
                datasets: [{
                    data: window.priorityData.map(d => d.count),
                    backgroundColor: [colors.danger, colors.warning, colors.success]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        labels: {
                            color: colors.text
                        }
                    }
                }
            }
        });
        
        // Weekly Productivity Chart
        const weeklyCtx = document.getElementById('weeklyChart').getContext('2d');
        new Chart(weeklyCtx, {
            type: 'bar',
            data: {
                labels: window.weeklyData.map(d => d.day_name),
                datasets: [{
                    label: window.translations.tasksCreated || 'Tasks Created',
                    data: window.weeklyData.map(d => d.tasks_created),
                    backgroundColor: colors.primary + '80'
                }, {
                    label: window.translations.tasksCompleted || 'Tasks Completed',
                    data: window.weeklyData.map(d => d.tasks_completed),
                    backgroundColor: colors.success + '80'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        labels: {
                            color: colors.text
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: { color: colors.text },
                        grid: { color: colors.grid }
                    },
                    y: {
                        ticks: { color: colors.text },
                        grid: { color: colors.grid }
                    }
                }
            }
        });
    </script>
    <script src="assets/js/theme.js"></script>
    <script src="assets/js/language.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>

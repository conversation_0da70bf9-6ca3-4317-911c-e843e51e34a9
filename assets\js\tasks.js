// Task management functions
let currentEditingTask = null;

// Open task modal for new task
function openTaskModal() {
    currentEditingTask = null;
    document.getElementById('modalTitle').textContent = translations.newTask;
    document.getElementById('taskForm').reset();
    document.getElementById('taskId').value = '';
    document.getElementById('taskDueDate').value = new Date().toISOString().split('T')[0];
    document.getElementById('taskModal').classList.remove('hidden');
    document.getElementById('taskModal').classList.add('flex');
}

// Open task modal for editing
function editTask(taskId) {
    // Fetch task data and populate form
    fetch(`api/get-task.php?id=${taskId}`)
        .then(response => response.json())
        .then(task => {
            if (task.error) {
                showMessage(task.error, 'error');
                return;
            }
            
            currentEditingTask = taskId;
            document.getElementById('modalTitle').textContent = translations.editTask;
            document.getElementById('taskId').value = task.id;
            document.getElementById('taskTitle').value = task.title;
            document.getElementById('taskDescription').value = task.description || '';
            document.getElementById('taskPriority').value = task.priority;
            document.getElementById('taskDueDate').value = task.due_date ? task.due_date.split(' ')[0] : '';
            
            document.getElementById('taskModal').classList.remove('hidden');
            document.getElementById('taskModal').classList.add('flex');
        })
        .catch(error => {
            console.error('Error fetching task:', error);
            showMessage('Failed to load task data', 'error');
        });
}

// Close task modal
function closeTaskModal() {
    document.getElementById('taskModal').classList.add('hidden');
    document.getElementById('taskModal').classList.remove('flex');
    currentEditingTask = null;
}

// Submit task form
document.getElementById('taskForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const submitBtn = document.getElementById('taskSubmitBtn');
    const originalText = submitBtn.textContent;
    submitBtn.disabled = true;
    submitBtn.textContent = translations.loading || 'Loading...';
    
    const formData = new FormData(this);
    const taskData = {
        id: formData.get('taskId'),
        title: formData.get('title'),
        description: formData.get('description'),
        priority: formData.get('priority'),
        due_date: formData.get('due_date')
    };
    
    try {
        const url = currentEditingTask ? 'api/update-task.php' : 'api/create-task.php';
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(taskData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage(result.message, 'success');
            closeTaskModal();
            location.reload(); // Refresh page to show updated tasks
        } else {
            showMessage(result.error || 'Failed to save task', 'error');
        }
    } catch (error) {
        console.error('Error saving task:', error);
        showMessage('Failed to save task', 'error');
    } finally {
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    }
});

// Quick add task form
document.getElementById('quickTaskForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const submitBtn = document.getElementById('quickTaskSubmit');
    const originalText = submitBtn.textContent;
    submitBtn.disabled = true;
    submitBtn.textContent = translations.adding || 'Adding...';
    
    const title = document.getElementById('quickTaskTitle').value;
    const priority = document.getElementById('quickTaskPriority').value;
    const dueDate = document.getElementById('quickTaskDate').value;
    
    const taskData = {
        title: title,
        priority: priority,
        due_date: dueDate
    };
    
    try {
        const response = await fetch('api/create-task.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(taskData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage(result.message, 'success');
            this.reset();
            document.getElementById('quickTaskDate').value = new Date().toISOString().split('T')[0];
            location.reload(); // Refresh page to show new task
        } else {
            showMessage(result.error || 'Failed to create task', 'error');
        }
    } catch (error) {
        console.error('Error creating task:', error);
        showMessage('Failed to create task', 'error');
    } finally {
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    }
});

// Toggle task status
async function toggleTaskStatus(taskId, currentStatus) {
    const newStatus = currentStatus === 'completed' ? 'pending' : 'completed';
    
    try {
        const response = await fetch('api/update-task.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                id: taskId,
                status: newStatus
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage(result.message, 'success');
            location.reload(); // Refresh page to show updated status
        } else {
            showMessage(result.error || 'Failed to update task', 'error');
        }
    } catch (error) {
        console.error('Error updating task status:', error);
        showMessage('Failed to update task', 'error');
    }
}

// Delete task
async function deleteTask(taskId) {
    if (!confirm(translations.confirm || 'Are you sure you want to delete this task?')) {
        return;
    }
    
    try {
        const response = await fetch('api/delete-task.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id: taskId })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage(result.message, 'success');
            location.reload(); // Refresh page to remove deleted task
        } else {
            showMessage(result.error || 'Failed to delete task', 'error');
        }
    } catch (error) {
        console.error('Error deleting task:', error);
        showMessage('Failed to delete task', 'error');
    }
}

// Close modal when clicking outside
document.getElementById('taskModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeTaskModal();
    }
});

// Close modal on Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && !document.getElementById('taskModal').classList.contains('hidden')) {
        closeTaskModal();
    }
});
// Main JavaScript file for PHP Productivity App

// Global variables
let translations = {};
let currentDate = new Date();

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    initializeCalendar();
});

// Initialize application
function initializeApp() {
    // Load translations (these will be populated by PHP)
    translations = window.translations || {};
    
    // Initialize theme
    loadTheme();
    
    // Initialize tooltips and other UI components
    initializeTooltips();
    
    // Set up event listeners
    setupEventListeners();
}

// Set up global event listeners
function setupEventListeners() {
    // Mobile sidebar toggle
    const sidebarToggle = document.getElementById('sidebarToggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', toggleSidebar);
    }
    
    // Global keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);
}

// Handle keyboard shortcuts
function handleKeyboardShortcuts(e) {
    // Ctrl/Cmd + K to open task modal
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        openTaskModal();
    }
    
    // Escape to close modals
    if (e.key === 'Escape') {
        closeAllModals();
    }
}

// Close all open modals
function closeAllModals() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.classList.add('hidden');
        modal.classList.remove('flex');
    });
}

// Show message function
function showMessage(message, type = 'info', duration = 5000) {
    const messageContainer = document.getElementById('messageContainer') || createMessageContainer();
    
    const messageElement = document.createElement('div');
    messageElement.className = `message-enter p-4 rounded-lg shadow-lg mb-3 ${getMessageClasses(type)}`;
    messageElement.textContent = message;
    
    messageContainer.appendChild(messageElement);
    
    // Animate in
    setTimeout(() => {
        messageElement.classList.remove('message-enter');
        messageElement.classList.add('message-enter-active');
    }, 10);
    
    // Auto remove
    setTimeout(() => {
        removeMessage(messageElement);
    }, duration);
    
    // Click to remove
    messageElement.addEventListener('click', () => {
        removeMessage(messageElement);
    });
}

// Create message container if it doesn't exist
function createMessageContainer() {
    const container = document.createElement('div');
    container.id = 'messageContainer';
    container.className = 'fixed top-20 right-4 z-50 max-w-sm';
    document.body.appendChild(container);
    return container;
}

// Get message CSS classes based on type
function getMessageClasses(type) {
    const baseClasses = 'cursor-pointer';
    switch (type) {
        case 'success':
            return `${baseClasses} bg-green-100 dark:bg-green-900 border border-green-300 dark:border-green-700 text-green-700 dark:text-green-300`;
        case 'error':
            return `${baseClasses} bg-red-100 dark:bg-red-900 border border-red-300 dark:border-red-700 text-red-700 dark:text-red-300`;
        case 'warning':
            return `${baseClasses} bg-yellow-100 dark:bg-yellow-900 border border-yellow-300 dark:border-yellow-700 text-yellow-700 dark:text-yellow-300`;
        default:
            return `${baseClasses} bg-blue-100 dark:bg-blue-900 border border-blue-300 dark:border-blue-700 text-blue-700 dark:text-blue-300`;
    }
}

// Remove message with animation
function removeMessage(messageElement) {
    messageElement.classList.remove('message-enter-active');
    messageElement.classList.add('message-exit-active');
    
    setTimeout(() => {
        if (messageElement.parentNode) {
            messageElement.parentNode.removeChild(messageElement);
        }
    }, 300);
}

// Initialize tooltips
function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

// Show tooltip
function showTooltip(e) {
    const tooltipText = e.target.getAttribute('data-tooltip');
    if (!tooltipText) return;
    
    const tooltip = document.createElement('div');
    tooltip.className = 'absolute bg-gray-900 text-white text-xs rounded py-1 px-2 z-50 pointer-events-none';
    tooltip.textContent = tooltipText;
    tooltip.id = 'tooltip';
    
    document.body.appendChild(tooltip);
    
    // Position tooltip
    const rect = e.target.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
}

// Hide tooltip
function hideTooltip() {
    const tooltip = document.getElementById('tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

// Toggle mobile sidebar
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        sidebar.classList.toggle('open');
    }
}

// Format date for display
function formatDate(dateString, locale = 'en') {
    const date = new Date(dateString);
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    };
    
    return date.toLocaleDateString(locale, options);
}

// Format time for display
function formatTime(dateString, locale = 'en') {
    const date = new Date(dateString);
    const options = { 
        hour: '2-digit', 
        minute: '2-digit' 
    };
    
    return date.toLocaleTimeString(locale, options);
}

// Debounce function for search and other inputs
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
}

// Loading state management
function setLoading(element, isLoading, originalText = '') {
    if (isLoading) {
        element.disabled = true;
        element.dataset.originalText = element.textContent;
        element.innerHTML = `
            <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            ${translations.loading || 'Loading...'}
        `;
    } else {
        element.disabled = false;
        element.textContent = originalText || element.dataset.originalText || element.textContent;
        delete element.dataset.originalText;
    }
}

// Animate element entrance
function animateIn(element, animationClass = 'animate-pulse') {
    element.classList.add(animationClass);
    setTimeout(() => {
        element.classList.remove(animationClass);
    }, 1000);
}

// Handle network errors
function handleNetworkError(error) {
    console.error('Network error:', error);
    showMessage(translations.networkError || 'Network error occurred. Please try again.', 'error');
}

// Local storage helpers
function setLocalStorage(key, value) {
    try {
        localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
        console.error('Error saving to localStorage:', error);
    }
}

function getLocalStorage(key, defaultValue = null) {
    try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
        console.error('Error reading from localStorage:', error);
        return defaultValue;
    }
}

// Initialize mini calendar
function initializeCalendar() {
    const calendarContainer = document.getElementById('miniCalendar');
    if (!calendarContainer) return;

    renderMiniCalendar();

    // Add navigation event listeners
    const prevBtn = document.getElementById('calendarPrev');
    const nextBtn = document.getElementById('calendarNext');

    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            currentDate.setMonth(currentDate.getMonth() - 1);
            renderMiniCalendar();
        });
    }

    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            currentDate.setMonth(currentDate.getMonth() + 1);
            renderMiniCalendar();
        });
    }
}

// Render mini calendar
function renderMiniCalendar() {
    const calendarGrid = document.getElementById('calendarGrid');
    const monthYear = document.getElementById('currentMonthYear');

    if (!calendarGrid || !monthYear) return;

    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    // Update month/year display
    const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
    ];
    monthYear.textContent = `${monthNames[month]} ${year}`;

    // Clear previous calendar
    calendarGrid.innerHTML = '';

    // Get first day of month and number of days
    const firstDay = new Date(year, month, 1).getDay();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const today = new Date();

    // Add empty cells for days before month starts
    for (let i = 0; i < firstDay; i++) {
        const emptyCell = document.createElement('div');
        emptyCell.className = 'h-8 w-8';
        calendarGrid.appendChild(emptyCell);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
        const dayCell = document.createElement('button');
        dayCell.className = 'h-8 w-8 text-sm rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors';
        dayCell.textContent = day;

        // Highlight today
        if (year === today.getFullYear() && month === today.getMonth() && day === today.getDate()) {
            dayCell.className += ' bg-primary-500 text-white hover:bg-primary-600';
        }

        // Add click handler to select date
        dayCell.addEventListener('click', () => {
            const selectedDate = new Date(year, month, day);
            selectCalendarDate(selectedDate);
        });

        calendarGrid.appendChild(dayCell);
    }
}

// Handle calendar date selection
function selectCalendarDate(date) {
    const dateString = date.toISOString().split('T')[0];

    // Update quick task form date if it exists
    const quickTaskDate = document.getElementById('quickTaskDate');
    if (quickTaskDate) {
        quickTaskDate.value = dateString;
    }

    // Update task modal date if it's open
    const taskDueDate = document.getElementById('taskDueDate');
    if (taskDueDate) {
        taskDueDate.value = dateString;
    }

    showMessage(`Selected date: ${formatDate(dateString)}`, 'info', 2000);
}

// AI Assistant functions
function openAIChat() {
    // Placeholder for AI chat functionality
    showMessage(translations.aiFeatureComingSoon || 'AI Assistant feature coming soon!', 'info');
}

// Export functions for use in other scripts
window.APP = {
    showMessage,
    setLoading,
    animateIn,
    formatDate,
    formatTime,
    debounce,
    setLocalStorage,
    getLocalStorage,
    handleNetworkError
};
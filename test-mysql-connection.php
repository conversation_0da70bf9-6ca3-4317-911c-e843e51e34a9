<?php
// Test MySQL connection and basic functionality
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>LifePilot MySQL Connection Test</h1>";

try {
    // Test database connection
    echo "<h2>1. Testing Database Connection</h2>";
    require_once 'config/db.php';
    echo "✅ Database connection successful!<br>";
    
    // Test table creation
    echo "<h2>2. Testing Table Creation</h2>";
    $tables = ['users', 'user_preferences', 'tasks', 'quotes'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ Table '$table' exists<br>";
        } else {
            echo "❌ Table '$table' does not exist<br>";
        }
    }
    
    // Test auth functions
    echo "<h2>3. Testing Auth Functions</h2>";
    require_once 'includes/auth.php';
    
    // Test user registration
    $testEmail = '<EMAIL>';
    $result = registerUser('Test', 'User', $testEmail, 'password123');
    if ($result === true) {
        echo "✅ User registration successful<br>";
        
        // Test user login
        $user = loginUser($testEmail, 'password123');
        if ($user) {
            echo "✅ User login successful<br>";
            $userId = $user['id'];
            
            // Test user preferences
            $preferences = getUserPreferences($userId);
            if ($preferences) {
                echo "✅ User preferences retrieved successfully<br>";
            }
            
            // Test task creation
            $taskId = createTask($userId, 'Test Task', 'This is a test task', 'medium', date('Y-m-d H:i:s'));
            if ($taskId) {
                echo "✅ Task creation successful (ID: $taskId)<br>";
                
                // Test task retrieval
                $todayTasks = getTodayTasks($userId);
                echo "✅ Today's tasks retrieved: " . count($todayTasks) . " tasks<br>";
                
                // Test task stats
                $stats = getTaskStats($userId);
                echo "✅ Task statistics retrieved: {$stats['total']} total tasks<br>";
                
                // Test task update
                $updateResult = updateTask($taskId, $userId, ['status' => 'completed']);
                if ($updateResult) {
                    echo "✅ Task update successful<br>";
                }
                
                // Test task deletion
                $deleteResult = deleteTask($taskId, $userId);
                if ($deleteResult) {
                    echo "✅ Task deletion successful<br>";
                }
            }
            
            // Clean up test user
            $stmt = $pdo->prepare("DELETE FROM users WHERE email = ?");
            $stmt->execute([$testEmail]);
            echo "✅ Test user cleaned up<br>";
            
        } else {
            echo "❌ User login failed<br>";
        }
    } else {
        echo "❌ User registration failed: $result<br>";
    }
    
    // Test quotes
    echo "<h2>4. Testing Quotes</h2>";
    $quote = getRandomQuote('en');
    if ($quote) {
        echo "✅ Random quote retrieved: \"" . htmlspecialchars($quote['text']) . "\" - " . htmlspecialchars($quote['author']) . "<br>";
    } else {
        echo "❌ Failed to retrieve random quote<br>";
    }
    
    echo "<h2>✅ All tests completed successfully!</h2>";
    echo "<p><strong>Your LifePilot application is ready to use with MySQL!</strong></p>";
    echo "<p><a href='login.php'>Go to Login Page</a></p>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error occurred:</h2>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Please check your MySQL configuration and ensure the database is set up correctly.</strong></p>";
}
?>

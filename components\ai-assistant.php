<?php
$quote = getRandomQuote($language);
?>

<div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
    <div class="flex items-center mb-4">
        <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364-.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white <?php echo $language === 'ar' ? 'mr-3' : 'ml-3'; ?>">
            <?php echo $lang['aiAssistant']; ?>
        </h3>
    </div>

    <!-- Daily Quote -->
    <div class="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-4 mb-4">
        <div class="flex items-start">
            <svg class="w-5 h-5 text-purple-500 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
            <div class="<?php echo $language === 'ar' ? 'mr-3' : 'ml-3'; ?>">
                <p class="text-sm text-gray-700 dark:text-gray-300 italic">
                    "<?php echo htmlspecialchars($quote['text'] ?? $lang['defaultQuote']); ?>"
                </p>
                <?php if ($quote && $quote['author']): ?>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                        - <?php echo htmlspecialchars($quote['author']); ?>
                    </p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- AI Suggestion -->
    <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 mb-4">
        <div class="flex items-start">
            <svg class="w-5 h-5 text-blue-500 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div class="<?php echo $language === 'ar' ? 'mr-3' : 'ml-3'; ?>">
                <p class="text-sm text-gray-700 dark:text-gray-300">
                    <?php echo $lang['aiSuggestion']; ?>
                </p>
            </div>
        </div>
    </div>

    <!-- Action Button -->
    <button onclick="openAIChat()" class="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white py-2 px-4 rounded-lg font-medium transition-all transform hover:scale-105">
        <?php echo $lang['openAIAssistant']; ?>
    </button>
</div>
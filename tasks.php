<?php
session_start();
require_once 'config/db.php';
require_once 'includes/auth.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// Get user data and preferences
$user = getUserById($_SESSION['user_id']);
$preferences = getUserPreferences($_SESSION['user_id']);
$theme = $preferences['theme'] ?? 'light';
$language = $preferences['language'] ?? 'ar';

// Include language file
require_once "lang/{$language}.php";

// Get filter parameters
$status_filter = $_GET['status'] ?? 'all';
$priority_filter = $_GET['priority'] ?? 'all';
$date_filter = $_GET['date'] ?? 'all';
$search = $_GET['search'] ?? '';

// Build query based on filters
$where_conditions = ['user_id = ?'];
$params = [$_SESSION['user_id']];

if ($status_filter !== 'all') {
    $where_conditions[] = 'status = ?';
    $params[] = $status_filter;
}

if ($priority_filter !== 'all') {
    $where_conditions[] = 'priority = ?';
    $params[] = $priority_filter;
}

if ($date_filter !== 'all') {
    switch ($date_filter) {
        case 'today':
            $where_conditions[] = 'DATE(due_date) = CURDATE()';
            break;
        case 'week':
            $where_conditions[] = 'due_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)';
            break;
        case 'overdue':
            $where_conditions[] = 'due_date < NOW() AND status != "completed"';
            break;
    }
}

if (!empty($search)) {
    $where_conditions[] = '(title LIKE ? OR description LIKE ?)';
    $params[] = "%$search%";
    $params[] = "%$search%";
}

$where_clause = implode(' AND ', $where_conditions);

// Get tasks with pagination
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

try {
    $pdo = getDB();
    
    // Get total count
    $count_sql = "SELECT COUNT(*) FROM tasks WHERE $where_clause";
    $count_stmt = $pdo->prepare($count_sql);
    $count_stmt->execute($params);
    $total_tasks = $count_stmt->fetchColumn();
    
    // Get tasks
    $sql = "SELECT * FROM tasks WHERE $where_clause 
            ORDER BY 
                CASE priority WHEN 'high' THEN 1 WHEN 'medium' THEN 2 WHEN 'low' THEN 3 END,
                due_date ASC, created_at DESC 
            LIMIT $per_page OFFSET $offset";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $tasks = $stmt->fetchAll();
    
    $total_pages = ceil($total_tasks / $per_page);
    
} catch (PDOException $e) {
    error_log("Tasks query error: " . $e->getMessage());
    $tasks = [];
    $total_tasks = 0;
    $total_pages = 1;
}
?>

<!DOCTYPE html>
<html lang="<?php echo $language; ?>" dir="<?php echo $language === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $lang['tasks']; ?> - <?php echo $lang['appTitle']; ?></title>
    <meta name="description" content="<?php echo $lang['appDescription']; ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a'
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="<?php echo $theme === 'dark' ? 'dark' : ''; ?> bg-gray-50 dark:bg-gray-900">
    <!-- Language Selector -->
    <?php include 'components/language-selector.php'; ?>
    
    <!-- Theme Toggle -->
    <?php include 'components/theme-toggle.php'; ?>
    
    <!-- Sidebar Navigation -->
    <?php include 'components/sidebar-nav.php'; ?>
    
    <div class="<?php echo $language === 'ar' ? 'mr-64' : 'ml-64'; ?> min-h-screen transition-all duration-300">
        <!-- Header -->
        <header class="bg-white dark:bg-gray-800 shadow-sm border-b dark:border-gray-700 px-6 py-4">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                        <?php echo $lang['tasks']; ?>
                    </h1>
                    <p class="text-gray-500 dark:text-gray-400">
                        <?php echo $total_tasks; ?> <?php echo $lang['tasks']; ?> <?php echo $lang['total'] ?? 'total'; ?>
                    </p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <button onclick="openTaskModal()" class="bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        <?php echo $lang['newTask']; ?>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="p-6">
            <!-- Filters Section -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow mb-6 p-6">
                <form method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                    <!-- Search -->
                    <div class="lg:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <?php echo $lang['search'] ?? 'Search'; ?>
                        </label>
                        <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>"
                               placeholder="<?php echo $lang['searchTasks'] ?? 'Search tasks...'; ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>
                    
                    <!-- Status Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <?php echo $lang['status']; ?>
                        </label>
                        <select name="status" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>><?php echo $lang['all'] ?? 'All'; ?></option>
                            <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>><?php echo $lang['pending']; ?></option>
                            <option value="in_progress" <?php echo $status_filter === 'in_progress' ? 'selected' : ''; ?>><?php echo $lang['inProgress']; ?></option>
                            <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>><?php echo $lang['completed']; ?></option>
                        </select>
                    </div>
                    
                    <!-- Priority Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <?php echo $lang['priority']; ?>
                        </label>
                        <select name="priority" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            <option value="all" <?php echo $priority_filter === 'all' ? 'selected' : ''; ?>><?php echo $lang['all'] ?? 'All'; ?></option>
                            <option value="high" <?php echo $priority_filter === 'high' ? 'selected' : ''; ?>><?php echo $lang['high']; ?></option>
                            <option value="medium" <?php echo $priority_filter === 'medium' ? 'selected' : ''; ?>><?php echo $lang['medium']; ?></option>
                            <option value="low" <?php echo $priority_filter === 'low' ? 'selected' : ''; ?>><?php echo $lang['low']; ?></option>
                        </select>
                    </div>
                    
                    <!-- Date Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <?php echo $lang['dueDate']; ?>
                        </label>
                        <select name="date" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            <option value="all" <?php echo $date_filter === 'all' ? 'selected' : ''; ?>><?php echo $lang['all'] ?? 'All'; ?></option>
                            <option value="today" <?php echo $date_filter === 'today' ? 'selected' : ''; ?>><?php echo $lang['today']; ?></option>
                            <option value="week" <?php echo $date_filter === 'week' ? 'selected' : ''; ?>><?php echo $lang['thisWeek']; ?></option>
                            <option value="overdue" <?php echo $date_filter === 'overdue' ? 'selected' : ''; ?>><?php echo $lang['overdue'] ?? 'Overdue'; ?></option>
                        </select>
                    </div>
                    
                    <!-- Filter Button -->
                    <div class="lg:col-span-5 flex gap-3">
                        <button type="submit" class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-2 rounded-lg transition-colors">
                            <?php echo $lang['filter'] ?? 'Filter'; ?>
                        </button>
                        <a href="tasks.php" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors">
                            <?php echo $lang['clear'] ?? 'Clear'; ?>
                        </a>
                    </div>
                </form>
            </div>

            <!-- Tasks List -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
                <?php if (empty($tasks)): ?>
                    <div class="text-center py-12">
                        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                            <?php echo $lang['noTasks'] ?? 'No tasks found'; ?>
                        </h3>
                        <p class="text-gray-500 dark:text-gray-400 mb-4">
                            <?php echo $lang['noTasksDescription'] ?? 'Create your first task to get started'; ?>
                        </p>
                        <button onclick="openTaskModal()" class="bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-lg transition-colors">
                            <?php echo $lang['createFirstTask'] ?? 'Create First Task'; ?>
                        </button>
                    </div>
                <?php else: ?>
                    <div class="divide-y divide-gray-200 dark:divide-gray-700">
                        <?php foreach ($tasks as $task): ?>
                            <div class="p-6 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                <div class="flex items-start justify-between">
                                    <div class="flex items-start flex-1">
                                        <!-- Task Checkbox -->
                                        <button onclick="toggleTaskStatus(<?php echo $task['id']; ?>, '<?php echo $task['status']; ?>')" 
                                                class="<?php echo $language === 'ar' ? 'ml-4' : 'mr-4'; ?> mt-1 w-5 h-5 rounded border-2 flex items-center justify-center transition-colors
                                                       <?php echo $task['status'] === 'completed' ? 'bg-green-500 border-green-500' : 'border-gray-300 hover:border-green-500'; ?>">
                                            <?php if ($task['status'] === 'completed'): ?>
                                                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                            <?php endif; ?>
                                        </button>
                                        
                                        <!-- Task Content -->
                                        <div class="flex-1 min-w-0">
                                            <h3 class="text-lg font-medium text-gray-900 dark:text-white <?php echo $task['status'] === 'completed' ? 'line-through text-gray-500' : ''; ?>">
                                                <?php echo htmlspecialchars($task['title']); ?>
                                            </h3>
                                            
                                            <?php if ($task['description']): ?>
                                                <p class="text-gray-600 dark:text-gray-400 mt-1">
                                                    <?php echo htmlspecialchars($task['description']); ?>
                                                </p>
                                            <?php endif; ?>
                                            
                                            <!-- Task Meta -->
                                            <div class="flex flex-wrap items-center gap-4 mt-3">
                                                <!-- Priority -->
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                           <?php 
                                                           switch($task['priority']) {
                                                               case 'high': echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'; break;
                                                               case 'medium': echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'; break;
                                                               case 'low': echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'; break;
                                                           }
                                                           ?>">
                                                    <?php echo $lang[$task['priority']]; ?>
                                                </span>
                                                
                                                <!-- Status -->
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                           <?php 
                                                           switch($task['status']) {
                                                               case 'completed': echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'; break;
                                                               case 'in_progress': echo 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'; break;
                                                               case 'pending': echo 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'; break;
                                                           }
                                                           ?>">
                                                    <?php echo $lang[$task['status']] ?? ucfirst(str_replace('_', ' ', $task['status'])); ?>
                                                </span>
                                                
                                                <!-- Due Date -->
                                                <?php if ($task['due_date']): ?>
                                                    <span class="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                                                        <svg class="w-4 h-4 <?php echo $language === 'ar' ? 'ml-1' : 'mr-1'; ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                        </svg>
                                                        <?php echo date('M j, Y', strtotime($task['due_date'])); ?>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Actions -->
                                    <div class="flex items-center gap-2 <?php echo $language === 'ar' ? 'mr-4' : 'ml-4'; ?>">
                                        <button onclick="editTask(<?php echo $task['id']; ?>)" 
                                                class="text-gray-400 hover:text-blue-500 transition-colors p-2"
                                                title="<?php echo $lang['edit']; ?>">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                        </button>
                                        <button onclick="deleteTask(<?php echo $task['id']; ?>)" 
                                                class="text-gray-400 hover:text-red-500 transition-colors p-2"
                                                title="<?php echo $lang['delete']; ?>">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <div class="px-6 py-4 border-t dark:border-gray-700">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    <?php echo $lang['showing'] ?? 'Showing'; ?> 
                                    <?php echo (($page - 1) * $per_page) + 1; ?> - 
                                    <?php echo min($page * $per_page, $total_tasks); ?> 
                                    <?php echo $lang['of'] ?? 'of'; ?> <?php echo $total_tasks; ?>
                                </div>
                                
                                <div class="flex gap-2">
                                    <?php if ($page > 1): ?>
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>" 
                                           class="px-3 py-2 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                                            <?php echo $lang['previous'] ?? 'Previous'; ?>
                                        </a>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>" 
                                           class="px-3 py-2 text-sm rounded-lg transition-colors <?php echo $i === $page ? 'bg-primary-500 text-white' : 'bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600'; ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $total_pages): ?>
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>" 
                                           class="px-3 py-2 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                                            <?php echo $lang['next'] ?? 'Next'; ?>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- Task Modal -->
    <?php include 'components/task-modal.php'; ?>

    <!-- Scripts -->
    <script>
        // Pass PHP translations to JavaScript
        window.translations = <?php echo json_encode($lang); ?>;
    </script>
    <script src="assets/js/theme.js"></script>
    <script src="assets/js/language.js"></script>
    <script src="assets/js/tasks.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>

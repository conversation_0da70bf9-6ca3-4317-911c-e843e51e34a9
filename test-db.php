<?php
echo "Testing SQLite database connection...\n";

// Test database connection
try {
    require_once 'config/db.php';
    echo "SQLite database connection successful!\n";
    
    // Test if tables exist
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    echo "Users table exists and accessible\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM quotes");
    $quoteCount = $stmt->fetchColumn();
    echo "Quotes table has $quoteCount records\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM tasks");
    $taskCount = $stmt->fetchColumn();
    echo "Tasks table has $taskCount records\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM user_preferences");
    $prefCount = $stmt->fetchColumn();
    echo "User preferences table has $prefCount records\n";
    
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?>
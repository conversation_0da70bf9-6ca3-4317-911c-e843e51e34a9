<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

session_start();
require_once '../config/db.php';
require_once '../includes/auth.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);
$message = trim($input['message'] ?? '');

if (empty($message)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Message is required']);
    exit();
}

try {
    // Get user data and preferences
    $user = getUserById($_SESSION['user_id']);
    $preferences = getUserPreferences($_SESSION['user_id']);
    $language = $preferences['language'] ?? 'ar';
    
    // Get user's task statistics for context
    $pdo = getDB();
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_tasks,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_tasks,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_tasks,
            COUNT(CASE WHEN DATE(due_date) = CURDATE() THEN 1 END) as today_tasks,
            COUNT(CASE WHEN due_date < NOW() AND status != 'completed' THEN 1 END) as overdue_tasks
        FROM tasks WHERE user_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $user_stats = $stmt->fetch();
    
    // Get recent tasks for context
    $stmt = $pdo->prepare("
        SELECT title, status, priority, created_at 
        FROM tasks 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $recent_tasks = $stmt->fetchAll();
    
    // Generate AI response based on message content and user context
    $response = generateAIResponse($message, $user, $user_stats, $recent_tasks, $language);
    
    // Log the conversation (optional - for improving AI responses)
    $stmt = $pdo->prepare("
        INSERT INTO ai_conversations (user_id, message, response, created_at) 
        VALUES (?, ?, ?, NOW())
    ");
    $stmt->execute([$_SESSION['user_id'], $message, $response]);
    
    echo json_encode([
        'success' => true,
        'response' => $response,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (PDOException $e) {
    error_log("AI Chat API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database error',
        'response' => 'I apologize, but I\'m experiencing some technical difficulties. Please try again later.'
    ]);
}

function generateAIResponse($message, $user, $stats, $recent_tasks, $language) {
    $lowerMessage = strtolower($message);
    $userName = $user['first_name'] ?: explode('@', $user['email'])[0];
    
    // Motivational responses
    if (strpos($lowerMessage, 'motivat') !== false || strpos($lowerMessage, 'inspire') !== false) {
        $motivationalMessages = [
            "🌟 {$userName}, you're doing amazing! You've completed {$stats['completed_tasks']} tasks so far. Every step forward counts!",
            "💪 Remember, progress isn't about perfection. You're building great habits one task at a time!",
            "🚀 Your dedication to productivity is inspiring. With {$stats['completed_tasks']} completed tasks, you're proving that consistency pays off!",
            "✨ Success is the sum of small efforts repeated day in and day out. You're on the right track!",
            "🎯 Focus on progress, not perfection. You've got {$stats['today_tasks']} tasks for today - you can do this!"
        ];
        return $motivationalMessages[array_rand($motivationalMessages)];
    }
    
    // Productivity tips
    if (strpos($lowerMessage, 'productiv') !== false || strpos($lowerMessage, 'tip') !== false) {
        $tips = [
            "📝 Try the Pomodoro Technique: Work for 25 minutes, then take a 5-minute break. It's great for maintaining focus!",
            "🎯 Use the Eisenhower Matrix to prioritize: Important & Urgent → Do first, Important & Not Urgent → Schedule, etc.",
            "🌅 Start your day by completing your most challenging task first - it's called 'eating the frog'!",
            "📱 Minimize distractions by turning off non-essential notifications during focused work time.",
            "✅ Break large tasks into smaller, manageable subtasks. It makes everything feel more achievable!",
            "🔄 Use the 2-minute rule: If something takes less than 2 minutes, do it immediately.",
            "📊 Review your tasks weekly to stay aligned with your goals and adjust priorities as needed."
        ];
        return $tips[array_rand($tips)];
    }
    
    // Task analysis
    if ((strpos($lowerMessage, 'task') !== false && strpos($lowerMessage, 'analyz') !== false) || 
        strpos($lowerMessage, 'progress') !== false || strpos($lowerMessage, 'stat') !== false) {
        
        $completionRate = $stats['total_tasks'] > 0 ? round(($stats['completed_tasks'] / $stats['total_tasks']) * 100) : 0;
        
        $analysis = "📊 Here's your task analysis, {$userName}:\n\n";
        $analysis .= "• Total tasks: {$stats['total_tasks']}\n";
        $analysis .= "• Completed: {$stats['completed_tasks']} ({$completionRate}%)\n";
        $analysis .= "• Pending: {$stats['pending_tasks']}\n";
        $analysis .= "• Today's tasks: {$stats['today_tasks']}\n";
        
        if ($stats['overdue_tasks'] > 0) {
            $analysis .= "• ⚠️ Overdue: {$stats['overdue_tasks']}\n\n";
            $analysis .= "💡 Suggestion: Focus on completing overdue tasks first to get back on track.";
        } else if ($completionRate >= 80) {
            $analysis .= "\n🎉 Excellent! You have a {$completionRate}% completion rate. Keep up the fantastic work!";
        } else if ($completionRate >= 60) {
            $analysis .= "\n👍 Good progress! You're at {$completionRate}% completion. Consider breaking down larger tasks into smaller ones.";
        } else {
            $analysis .= "\n💪 You're at {$completionRate}% completion. Try focusing on 2-3 important tasks today to build momentum.";
        }
        
        return $analysis;
    }
    
    // Task suggestions
    if (strpos($lowerMessage, 'suggest') !== false && strpos($lowerMessage, 'task') !== false) {
        $suggestions = [
            "📚 Review and organize your workspace for better productivity",
            "💡 Plan tomorrow's top 3 priorities",
            "🏃 Take a 10-minute walk or exercise break",
            "📖 Read for 15 minutes on a topic you're interested in",
            "🧘 Practice 5 minutes of mindfulness or meditation",
            "📞 Reach out to a colleague, friend, or family member",
            "🎯 Set a specific, measurable goal for next week",
            "📝 Write down three things you're grateful for today",
            "🔄 Review and update your task priorities",
            "💻 Learn something new for 20 minutes"
        ];
        
        $randomSuggestions = array_rand($suggestions, 3);
        $suggestionList = "";
        foreach ($randomSuggestions as $index) {
            $suggestionList .= $suggestions[$index] . "\n";
        }
        
        return "💡 Here are some task suggestions for you, {$userName}:\n\n{$suggestionList}\nWhich one sounds interesting to you?";
    }
    
    // Greetings
    if (strpos($lowerMessage, 'hello') !== false || strpos($lowerMessage, 'hi') !== false || 
        strpos($lowerMessage, 'hey') !== false || strpos($lowerMessage, 'good morning') !== false) {
        $greetings = [
            "👋 Hello {$userName}! How can I help you be more productive today?",
            "🌟 Hi there! Ready to tackle your tasks? You have {$stats['today_tasks']} tasks for today.",
            "💪 Hey {$userName}! Let's make today productive! What would you like to work on?"
        ];
        return $greetings[array_rand($greetings)];
    }
    
    // Help requests
    if (strpos($lowerMessage, 'help') !== false) {
        return "🤖 I can help you with:\n\n" .
               "• Motivational messages and quotes\n" .
               "• Productivity tips and techniques\n" .
               "• Task analysis and insights\n" .
               "• Task suggestions and ideas\n" .
               "• Progress tracking and statistics\n\n" .
               "Just ask me anything about productivity, {$userName}!";
    }
    
    // Quote requests
    if (strpos($lowerMessage, 'quote') !== false) {
        // This would typically call the quote API, but for simplicity, we'll return a random quote
        $quotes = [
            "💬 \"The way to get started is to quit talking and begin doing.\" — Walt Disney",
            "💬 \"Success is not final, failure is not fatal: it is the courage to continue that counts.\" — Winston Churchill",
            "💬 \"The future depends on what you do today.\" — Mahatma Gandhi",
            "💬 \"Don't watch the clock; do what it does. Keep going.\" — Sam Levenson",
            "💬 \"The secret of getting ahead is getting started.\" — Mark Twain"
        ];
        return $quotes[array_rand($quotes)];
    }
    
    // Default responses
    $defaultResponses = [
        "🤔 That's interesting! Can you tell me more about what you'd like help with regarding your productivity?",
        "💭 I'm here to help with your productivity journey. Try asking about tasks, motivation, or tips!",
        "🎯 I can help you with task management, productivity tips, or motivation. What would you like to know?",
        "✨ Feel free to ask me about your tasks, productivity strategies, or if you need some motivation, {$userName}!",
        "🚀 I'm your productivity companion! Ask me about task analysis, motivation, tips, or anything productivity-related."
    ];
    
    return $defaultResponses[array_rand($defaultResponses)];
}
?>

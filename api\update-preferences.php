<?php
session_start();
require_once '../config/db.php';
require_once '../includes/auth.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

// Handle GET request for language change via URL parameter
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['language'])) {
    $language = $_GET['language'];
    if (in_array($language, ['ar', 'en', 'fr'])) {
        $preferences = getUserPreferences($_SESSION['user_id']);
        $result = updateUserPreferences($_SESSION['user_id'], $preferences['theme'], $language);
        
        if ($result) {
            header('Location: ../index.php');
            exit();
        } else {
            header('Location: ../index.php?error=preferences_update_failed');
            exit();
        }
    }
    header('Location: ../index.php');
    exit();
}

// Handle POST request for JSON updates
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON input']);
        exit();
    }
    
    $preferences = getUserPreferences($_SESSION['user_id']);
    $theme = $preferences['theme'];
    $language = $preferences['language'];
    
    // Update preferences based on input
    if (isset($input['theme']) && in_array($input['theme'], ['light', 'dark'])) {
        $theme = $input['theme'];
    }
    
    if (isset($input['language']) && in_array($input['language'], ['ar', 'en', 'fr'])) {
        $language = $input['language'];
    }
    
    try {
        $result = updateUserPreferences($_SESSION['user_id'], $theme, $language);
        
        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'Preferences updated successfully'
            ]);
        } else {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to update preferences']);
        }
    } catch (Exception $e) {
        error_log("Update preferences error: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Internal server error']);
    }
}
?>
# LifePilot - Task & Productivity Manager

A comprehensive web-based productivity application built with PHP and MySQL, featuring task management, progress tracking, and AI assistance.

## 🌟 Features

### Core Functionality
- **User Authentication**: Secure registration and login system
- **Task Management**: Create, edit, delete, and organize tasks
- **Priority System**: Set task priorities (Low, Medium, High)
- **Due Date Tracking**: Schedule tasks with due dates
- **Status Management**: Track task progress (Pending, In Progress, Completed)

### Dashboard & Analytics
- **Real-time Statistics**: View completion rates and task counts
- **Weekly Progress**: Track productivity over time
- **Today's Tasks**: Quick overview of daily tasks
- **Progress Visualization**: Charts and statistics

### User Experience
- **Multi-language Support**: Arabic, English, and French
- **Dark/Light Theme**: Toggle between themes
- **Responsive Design**: Works on desktop and mobile
- **Quick Actions**: Fast task creation and management

### Additional Features
- **Mini Calendar**: Visual date picker and navigation
- **AI Assistant**: Motivational quotes and productivity tips
- **Real-time Updates**: Dynamic content updates
- **Keyboard Shortcuts**: Efficient navigation (Ctrl+K for new task)

## 🚀 Quick Start

### Prerequisites
- XAMPP (or similar LAMP/WAMP stack)
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web browser

### Installation

1. **Clone/Download** the project to your XAMPP htdocs directory:
   ```
   c:\xampp\htdocs\LifePilot\
   ```

2. **Start XAMPP** services (Apache and MySQL)

3. **Create Database**:
   - Open phpMyAdmin (`http://localhost/phpmyadmin`)
   - Create database named `lifepilot_db`
   - Import `database/lifepilot_mysql_setup.sql`

4. **Configure Database** (if needed):
   - Edit `config/db.php` with your MySQL credentials

5. **Test Installation**:
   - Visit `http://localhost/LifePilot/test-mysql-connection.php`
   - Verify all tests pass

6. **Access Application**:
   - Go to `http://localhost/LifePilot/`
   - Login with default credentials:
     - Email: `<EMAIL>`
     - Password: `admin123`

## 📁 Project Structure

```
LifePilot/
├── api/                    # API endpoints
│   ├── create-task.php
│   ├── update-task.php
│   ├── delete-task.php
│   ├── get-task.php
│   └── update-preferences.php
├── assets/                 # Static assets
│   ├── css/
│   │   └── style.css
│   └── js/
│       ├── main.js
│       ├── tasks.js
│       ├── theme.js
│       └── language.js
├── components/             # Reusable components
│   ├── sidebar-nav.php
│   ├── task-modal.php
│   ├── todays-tasks.php
│   ├── quick-add-task.php
│   ├── mini-calendar.php
│   ├── ai-assistant.php
│   └── progress-stats.php
├── config/                 # Configuration
│   └── db.php
├── database/               # Database files
│   └── lifepilot_mysql_setup.sql
├── includes/               # Core functions
│   └── auth.php
├── lang/                   # Language files
│   ├── ar.php
│   ├── en.php
│   └── fr.php
├── index.php               # Main dashboard
├── login.php               # Authentication page
├── logout.php              # Logout handler
└── test-mysql-connection.php # Test script
```

## 🛠️ Technology Stack

- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Styling**: Tailwind CSS (CDN)
- **Icons**: Heroicons (SVG)

## 🔧 Configuration

### Database Settings
Edit `config/db.php`:
```php
$host = 'localhost';
$dbname = 'lifepilot_db';
$username = 'root';
$password = '';
```

### Language Support
Add new languages by creating files in `lang/` directory following the existing structure.

### Theme Customization
Modify CSS variables in `assets/css/style.css` or extend Tailwind configuration.

## 📊 Database Schema

### Tables
- **users**: User accounts and profiles
- **user_preferences**: Theme and language settings
- **tasks**: Task data and metadata
- **quotes**: Motivational quotes for AI assistant

### Key Features
- Foreign key constraints for data integrity
- Indexes for optimal query performance
- UTF-8 support for international content
- Automatic timestamps for audit trails

## 🎯 Usage Guide

### Creating Tasks
1. Click "New Task" button or use Ctrl+K
2. Fill in task details (title, description, priority, due date)
3. Click "Save" to create the task

### Managing Tasks
- **Edit**: Click the edit icon on any task
- **Complete**: Click the checkbox to mark as done
- **Delete**: Click the delete icon (with confirmation)

### Quick Add
Use the "Quick Add Task" widget in the sidebar for fast task creation.

### Calendar Integration
- Click dates in the mini calendar to set due dates
- Navigate months using arrow buttons

## 🔒 Security Features

- Password hashing using PHP's `password_hash()`
- SQL injection prevention with prepared statements
- XSS protection with `htmlspecialchars()`
- Session-based authentication
- CSRF protection on forms

## 🌐 Browser Support

- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+

## 📱 Mobile Support

Fully responsive design optimized for:
- Smartphones (320px+)
- Tablets (768px+)
- Desktop (1024px+)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section in `DATABASE_SETUP_INSTRUCTIONS.md`
2. Review browser console for JavaScript errors
3. Check PHP error logs
4. Verify database connection and table structure

## 🔄 Updates

### Version 2.0 (Current)
- ✅ Migrated from SQLite to MySQL
- ✅ Enhanced UI/UX with Tailwind CSS
- ✅ Added multi-language support
- ✅ Implemented dark/light theme toggle
- ✅ Added mini calendar widget
- ✅ Improved mobile responsiveness

### Planned Features
- Email notifications
- Task categories and tags
- File attachments
- Team collaboration
- Advanced reporting
- API documentation

---

**Made with ❤️ for productivity enthusiasts**

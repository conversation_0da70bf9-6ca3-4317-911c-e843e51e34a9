<div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6"><?php echo $lang['weeklyStats']; ?></h3>

    <?php if (empty($weeklyProgress)): ?>
        <div class="text-center py-8">
            <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
            </div>
            <p class="text-gray-500 dark:text-gray-400"><?php echo $lang['noWeeklyData']; ?></p>
        </div>
    <?php else: ?>
        <!-- Weekly Progress Chart -->
        <div class="mb-6">
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3"><?php echo $lang['weeklyTaskProgress']; ?></h4>
            <div class="space-y-3">
                <?php foreach ($weeklyProgress as $day): ?>
                    <?php 
                    $completionPercentage = $day['total'] > 0 ? ($day['completed'] / $day['total']) * 100 : 0;
                    $dayName = date('l', strtotime($day['date']));
                    $dayNameKey = strtolower($dayName);
                    ?>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center flex-1">
                            <span class="text-sm text-gray-600 dark:text-gray-400 w-20">
                                <?php echo $lang[$dayNameKey]; ?>
                            </span>
                            <div class="flex-1 mx-4">
                                <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden">
                                    <div class="bg-primary-500 h-2 rounded-full transition-all duration-300" 
                                         style="width: <?php echo $completionPercentage; ?>%"></div>
                                </div>
                            </div>
                            <span class="text-sm text-gray-500 dark:text-gray-400">
                                <?php echo $day['completed']; ?>/<?php echo $day['total']; ?>
                            </span>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Summary Stats -->
        <div class="grid grid-cols-2 gap-4">
            <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                    <?php echo array_sum(array_column($weeklyProgress, 'completed')); ?>
                </div>
                <div class="text-sm text-green-700 dark:text-green-300">
                    <?php echo $lang['completed']; ?>
                </div>
            </div>
            <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    <?php echo array_sum(array_column($weeklyProgress, 'total')); ?>
                </div>
                <div class="text-sm text-blue-700 dark:text-blue-300">
                    <?php echo $lang['todayTasks']; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>
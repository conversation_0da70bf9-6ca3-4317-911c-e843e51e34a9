<?php
session_start();
require_once 'config/db.php';
require_once 'includes/auth.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// Get user data and preferences
$user = getUserById($_SESSION['user_id']);
$preferences = getUserPreferences($_SESSION['user_id']);
$theme = $preferences['theme'] ?? 'light';
$language = $preferences['language'] ?? 'ar';

// Include language file
require_once "lang/{$language}.php";

// Get user's task statistics for AI context
try {
    $pdo = getDB();
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_tasks,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_tasks,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_tasks,
            COUNT(CASE WHEN DATE(due_date) = CURDATE() THEN 1 END) as today_tasks,
            COUNT(CASE WHEN due_date < NOW() AND status != 'completed' THEN 1 END) as overdue_tasks
        FROM tasks WHERE user_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $user_stats = $stmt->fetch();
    
    // Get recent tasks for context
    $stmt = $pdo->prepare("
        SELECT title, status, priority, created_at 
        FROM tasks 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $recent_tasks = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $user_stats = ['total_tasks' => 0, 'completed_tasks' => 0, 'pending_tasks' => 0, 'today_tasks' => 0, 'overdue_tasks' => 0];
    $recent_tasks = [];
}
?>

<!DOCTYPE html>
<html lang="<?php echo $language; ?>" dir="<?php echo $language === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $lang['aiAssistant']; ?> - <?php echo $lang['appTitle']; ?></title>
    <meta name="description" content="<?php echo $lang['appDescription']; ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a'
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="<?php echo $theme === 'dark' ? 'dark' : ''; ?> bg-gray-50 dark:bg-gray-900">
    <!-- Language Selector -->
    <?php include 'components/language-selector.php'; ?>
    
    <!-- Theme Toggle -->
    <?php include 'components/theme-toggle.php'; ?>
    
    <!-- Sidebar Navigation -->
    <?php include 'components/sidebar-nav.php'; ?>
    
    <div class="<?php echo $language === 'ar' ? 'lg:mr-64' : 'lg:ml-64'; ?> min-h-screen transition-all duration-300">
        <!-- Header -->
        <header class="bg-white dark:bg-gray-800 shadow-sm border-b dark:border-gray-700 px-6 py-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                        <?php echo $lang['aiAssistant']; ?>
                    </h1>
                    <p class="text-gray-500 dark:text-gray-400">
                        <?php echo $lang['aiAssistantDesc'] ?? 'Your personal productivity companion'; ?>
                    </p>
                </div>
                <div class="flex items-center gap-3">
                    <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                    <span class="text-sm text-gray-600 dark:text-gray-400">
                        <?php echo $lang['online'] ?? 'Online'; ?>
                    </span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- Chat Interface -->
                <div class="lg:col-span-3">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow h-[600px] flex flex-col">
                        <!-- Chat Header -->
                        <div class="p-4 border-b dark:border-gray-700">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900 dark:text-white">
                                        <?php echo $lang['lifePilotAI'] ?? 'LifePilot AI'; ?>
                                    </h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        <?php echo $lang['aiReady'] ?? 'Ready to help you be more productive'; ?>
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Chat Messages -->
                        <div id="chatMessages" class="flex-1 p-4 overflow-y-auto space-y-4">
                            <!-- Welcome Message -->
                            <div class="flex items-start gap-3">
                                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                    </svg>
                                </div>
                                <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 max-w-md">
                                    <p class="text-gray-900 dark:text-white">
                                        <?php echo $lang['aiWelcome'] ?? 'Hello! I\'m your LifePilot AI assistant. I can help you with task management, productivity tips, and motivation. How can I assist you today?'; ?>
                                    </p>
                                    <span class="text-xs text-gray-500 dark:text-gray-400 mt-2 block">
                                        <?php echo date('H:i'); ?>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Typing Indicator -->
                        <div id="typingIndicator" class="px-4 pb-2 hidden">
                            <div class="flex items-center gap-3">
                                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                    </svg>
                                </div>
                                <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
                                    <div class="flex space-x-1">
                                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Chat Input -->
                        <div class="p-4 border-t dark:border-gray-700">
                            <form id="chatForm" class="flex gap-3">
                                <input type="text" id="chatInput" placeholder="<?php echo $lang['askAnything'] ?? 'Ask me anything about productivity...'; ?>" 
                                       class="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                <button type="submit" class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-2 rounded-lg transition-colors flex items-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                    </svg>
                                    <?php echo $lang['send'] ?? 'Send'; ?>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Quick Actions -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            <?php echo $lang['quickActions'] ?? 'Quick Actions'; ?>
                        </h3>
                        <div class="space-y-3">
                            <button onclick="askAI('<?php echo $lang['motivateMe'] ?? 'Give me some motivation'; ?>')" 
                                    class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                                <div class="flex items-center gap-3">
                                    <svg class="w-5 h-5 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                    </svg>
                                    <span class="text-gray-900 dark:text-white"><?php echo $lang['motivateMe'] ?? 'Motivate Me'; ?></span>
                                </div>
                            </button>

                            <button onclick="askAI('<?php echo $lang['productivityTips'] ?? 'Give me productivity tips'; ?>')" 
                                    class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                                <div class="flex items-center gap-3">
                                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                    <span class="text-gray-900 dark:text-white"><?php echo $lang['productivityTips'] ?? 'Productivity Tips'; ?></span>
                                </div>
                            </button>

                            <button onclick="askAI('<?php echo $lang['analyzeTasks'] ?? 'Analyze my tasks'; ?>')" 
                                    class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                                <div class="flex items-center gap-3">
                                    <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    <span class="text-gray-900 dark:text-white"><?php echo $lang['analyzeTasks'] ?? 'Analyze Tasks'; ?></span>
                                </div>
                            </button>

                            <button onclick="askAI('<?php echo $lang['suggestTasks'] ?? 'Suggest new tasks'; ?>')" 
                                    class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                                <div class="flex items-center gap-3">
                                    <svg class="w-5 h-5 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    <span class="text-gray-900 dark:text-white"><?php echo $lang['suggestTasks'] ?? 'Suggest Tasks'; ?></span>
                                </div>
                            </button>
                        </div>
                    </div>

                    <!-- Daily Quote -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            <?php echo $lang['dailyQuote'] ?? 'Daily Quote'; ?>
                        </h3>
                        <div id="dailyQuote" class="text-center">
                            <div class="animate-pulse">
                                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full mb-2"></div>
                                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"></div>
                                <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mx-auto"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Your Stats -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            <?php echo $lang['yourStats'] ?? 'Your Stats'; ?>
                        </h3>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400"><?php echo $lang['totalTasks']; ?></span>
                                <span class="font-semibold text-gray-900 dark:text-white"><?php echo $user_stats['total_tasks']; ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400"><?php echo $lang['completed']; ?></span>
                                <span class="font-semibold text-green-600"><?php echo $user_stats['completed_tasks']; ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400"><?php echo $lang['pending']; ?></span>
                                <span class="font-semibold text-yellow-600"><?php echo $user_stats['pending_tasks']; ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400"><?php echo $lang['today']; ?></span>
                                <span class="font-semibold text-blue-600"><?php echo $user_stats['today_tasks']; ?></span>
                            </div>
                            <?php if ($user_stats['overdue_tasks'] > 0): ?>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400"><?php echo $lang['overdue']; ?></span>
                                <span class="font-semibold text-red-600"><?php echo $user_stats['overdue_tasks']; ?></span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script>
        // Pass PHP data to JavaScript
        window.translations = <?php echo json_encode($lang); ?>;
        window.userStats = <?php echo json_encode($user_stats); ?>;
        window.recentTasks = <?php echo json_encode($recent_tasks); ?>;
        window.userName = '<?php echo htmlspecialchars($user['first_name'] ?: explode('@', $user['email'])[0]); ?>';
    </script>
    <script src="assets/js/theme.js"></script>
    <script src="assets/js/language.js"></script>
    <script src="assets/js/ai-assistant.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>

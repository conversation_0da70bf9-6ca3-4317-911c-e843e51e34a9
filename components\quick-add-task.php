<div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4"><?php echo $lang['quickAddTask']; ?></h3>
    
    <form id="quickTaskForm" class="space-y-4">
        <div>
            <input type="text" 
                   id="quickTaskTitle" 
                   placeholder="<?php echo $lang['taskPlaceholder']; ?>" 
                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                   required>
        </div>
        
        <div class="grid grid-cols-2 gap-3">
            <select id="quickTaskPriority" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                <option value="medium"><?php echo $lang['medium']; ?></option>
                <option value="high"><?php echo $lang['high']; ?></option>
                <option value="low"><?php echo $lang['low']; ?></option>
            </select>
            
            <input type="date" 
                   id="quickTaskDate" 
                   value="<?php echo date('Y-m-d'); ?>"
                   class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
        </div>
        
        <button type="submit" 
                class="w-full bg-primary-500 hover:bg-primary-600 text-white py-2 px-4 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                id="quickTaskSubmit">
            <?php echo $lang['addTask']; ?>
        </button>
    </form>
</div>
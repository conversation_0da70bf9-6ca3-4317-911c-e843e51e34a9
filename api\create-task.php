<?php
session_start();
require_once '../config/db.php';
require_once '../includes/auth.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid JSON input']);
    exit();
}

// Validate required fields
if (empty($input['title'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Task title is required']);
    exit();
}

// Set default values
$title = trim($input['title']);
$description = trim($input['description'] ?? '');
$priority = $input['priority'] ?? 'medium';
$dueDate = $input['due_date'] ?? date('Y-m-d');

// Validate priority
if (!in_array($priority, ['low', 'medium', 'high'])) {
    $priority = 'medium';
}

// Convert due date to datetime
$dueDatetime = $dueDate . ' 23:59:59';

try {
    $taskId = createTask($_SESSION['user_id'], $title, $description, $priority, $dueDatetime);
    
    if ($taskId) {
        echo json_encode([
            'success' => true,
            'message' => 'Task created successfully',
            'task_id' => $taskId
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create task']);
    }
} catch (Exception $e) {
    error_log("Create task error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
<?php
// Get error details
$error_code = $_GET['code'] ?? 500;
$error_message = $_GET['message'] ?? '';

// Set appropriate HTTP status code
http_response_code($error_code);

// Get language preference from URL or default to Arabic
$language = $_GET['lang'] ?? 'ar';
if (!in_array($language, ['ar', 'en', 'fr'])) {
    $language = 'ar';
}

// Include language file
require_once "lang/{$language}.php";

// Get theme from cookie or default to light
$theme = $_COOKIE['theme'] ?? 'light';

// Error types
$error_types = [
    400 => $lang['badRequest'] ?? 'Bad Request',
    401 => $lang['unauthorized'] ?? 'Unauthorized',
    403 => $lang['forbidden'] ?? 'Forbidden',
    404 => $lang['pageNotFound'] ?? 'Page Not Found',
    500 => $lang['serverError'] ?? 'Server Error',
    503 => $lang['serviceUnavailable'] ?? 'Service Unavailable'
];

$error_title = $error_types[$error_code] ?? $lang['unknownError'] ?? 'Unknown Error';
?>

<!DOCTYPE html>
<html lang="<?php echo $language; ?>" dir="<?php echo $language === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $error_title; ?> - <?php echo $lang['appTitle']; ?></title>
    <meta name="description" content="<?php echo $lang['errorDesc'] ?? 'An error occurred while processing your request.'; ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a'
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="<?php echo $theme === 'dark' ? 'dark' : ''; ?> bg-gray-50 dark:bg-gray-900 min-h-screen flex items-center justify-center">
    <!-- Language Selector -->
    <div class="fixed top-4 <?php echo $language === 'ar' ? 'right-4' : 'left-4'; ?> z-50">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-2 border dark:border-gray-700">
            <div class="flex gap-2">
                <a href="?code=<?php echo $error_code; ?>&message=<?php echo urlencode($error_message); ?>&lang=ar" class="w-8 h-6 rounded overflow-hidden border-2 <?php echo $language === 'ar' ? 'border-primary-500' : 'border-transparent hover:border-gray-300'; ?>" title="العربية">
                    <img src="https://flagcdn.com/w40/sa.jpg" alt="العربية" class="w-full h-full object-cover">
                </a>
                <a href="?code=<?php echo $error_code; ?>&message=<?php echo urlencode($error_message); ?>&lang=en" class="w-8 h-6 rounded overflow-hidden border-2 <?php echo $language === 'en' ? 'border-primary-500' : 'border-transparent hover:border-gray-300'; ?>" title="English">
                    <img src="https://flagcdn.com/w40/us.jpg" alt="English" class="w-full h-full object-cover">
                </a>
                <a href="?code=<?php echo $error_code; ?>&message=<?php echo urlencode($error_message); ?>&lang=fr" class="w-8 h-6 rounded overflow-hidden border-2 <?php echo $language === 'fr' ? 'border-primary-500' : 'border-transparent hover:border-gray-300'; ?>" title="Français">
                    <img src="https://flagcdn.com/w40/fr.jpg" alt="Français" class="w-full h-full object-cover">
                </a>
            </div>
        </div>
    </div>

    <!-- Theme Toggle -->
    <div class="fixed top-4 <?php echo $language === 'ar' ? 'left-4' : 'right-4'; ?> z-50">
        <button onclick="toggleTheme()" class="bg-white dark:bg-gray-800 p-3 rounded-full shadow-lg border dark:border-gray-700 hover:shadow-xl transition-all duration-300">
            <svg class="h-5 w-5 text-gray-700 dark:hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
            </svg>
            <svg class="h-5 w-5 text-yellow-400 hidden dark:block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
            </svg>
        </button>
    </div>

    <!-- Main Content -->
    <div class="text-center px-4 max-w-lg mx-auto">
        <!-- Error Illustration -->
        <div class="mb-8">
            <div class="relative">
                <div class="text-9xl font-bold text-primary-500 opacity-20 select-none"><?php echo $error_code; ?></div>
                <div class="absolute inset-0 flex items-center justify-center">
                    <?php if ($error_code >= 500): ?>
                        <svg class="w-24 h-24 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    <?php elseif ($error_code == 404): ?>
                        <svg class="w-24 h-24 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 20.5a7.962 7.962 0 01-5-1.709M15 3.5a7.966 7.966 0 00-6 0M9 3.5a7.966 7.966 0 006 0"></path>
                        </svg>
                    <?php elseif ($error_code == 403): ?>
                        <svg class="w-24 h-24 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                    <?php else: ?>
                        <svg class="w-24 h-24 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Error Message -->
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            <?php echo $error_title; ?>
        </h1>
        
        <p class="text-lg text-gray-600 dark:text-gray-400 mb-8">
            <?php 
            if (!empty($error_message)) {
                echo htmlspecialchars($error_message);
            } else {
                switch ($error_code) {
                    case 400:
                        echo $lang['badRequestMessage'] ?? 'The server could not understand the request due to invalid syntax.';
                        break;
                    case 401:
                        echo $lang['unauthorizedMessage'] ?? 'You need to be logged in to access this page.';
                        break;
                    case 403:
                        echo $lang['forbiddenMessage'] ?? 'You do not have permission to access this resource.';
                        break;
                    case 404:
                        echo $lang['pageNotFoundMessage'] ?? 'The page you are looking for could not be found.';
                        break;
                    case 500:
                        echo $lang['serverErrorMessage'] ?? 'An internal server error occurred. Please try again later.';
                        break;
                    case 503:
                        echo $lang['serviceUnavailableMessage'] ?? 'The service is temporarily unavailable. Please try again later.';
                        break;
                    default:
                        echo $lang['unknownErrorMessage'] ?? 'An unknown error occurred. Please try again later.';
                }
            }
            ?>
        </p>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="javascript:history.back()" 
               class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <svg class="w-4 h-4 <?php echo $language === 'ar' ? 'ml-2' : 'mr-2'; ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                <?php echo $lang['goBack'] ?? 'Go Back'; ?>
            </a>
            
            <a href="index.php" 
               class="inline-flex items-center justify-center px-6 py-3 bg-primary-500 hover:bg-primary-600 text-white rounded-lg transition-colors">
                <svg class="w-4 h-4 <?php echo $language === 'ar' ? 'ml-2' : 'mr-2'; ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                <?php echo $lang['goHome'] ?? 'Go Home'; ?>
            </a>
        </div>

        <!-- Support Information -->
        <div class="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                <?php echo $lang['needHelp'] ?? 'Need help?'; ?>
            </p>
            <a href="about.php" class="inline-flex items-center justify-center px-6 py-2 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
                <svg class="w-4 h-4 <?php echo $language === 'ar' ? 'ml-2' : 'mr-2'; ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <?php echo $lang['helpSupport'] ?? 'Help & Support'; ?>
            </a>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Theme handling
        function toggleTheme() {
            document.documentElement.classList.toggle('dark');
            const isDark = document.documentElement.classList.contains('dark');
            document.cookie = `theme=${isDark ? 'dark' : 'light'}; path=/; max-age=31536000`;
        }

        // Load saved theme
        const savedTheme = document.cookie.split('; ').find(row => row.startsWith('theme='));
        if (savedTheme) {
            const theme = savedTheme.split('=')[1];
            if (theme === 'dark') {
                document.documentElement.classList.add('dark');
            }
        }
    </script>
</body>
</html>

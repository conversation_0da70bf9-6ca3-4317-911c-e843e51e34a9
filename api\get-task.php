<?php
session_start();
require_once '../config/db.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

// Get task ID from query parameter
$taskId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$taskId) {
    http_response_code(400);
    echo json_encode(['error' => 'Task ID is required']);
    exit();
}

try {
    $pdo = getDB();
    $stmt = $pdo->prepare("
        SELECT id, title, description, priority, status, due_date, created_at 
        FROM tasks 
        WHERE id = ? AND user_id = ?
    ");
    $stmt->execute([$taskId, $_SESSION['user_id']]);
    $task = $stmt->fetch();
    
    if ($task) {
        echo json_encode($task);
    } else {
        http_response_code(404);
        echo json_encode(['error' => 'Task not found']);
    }
} catch (Exception $e) {
    error_log("Get task error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
# LifePilot - MySQL Database Setup Instructions

## Overview
This document provides step-by-step instructions to set up the LifePilot productivity application with MySQL database using phpMyAdmin.

## Prerequisites
- XAMPP (or similar) with Apache, MySQL, and PHP installed
- phpMyAdmin accessible
- Web browser

## Step 1: Database Configuration

### 1.1 Update Database Credentials
Open `config/db.php` and update the database credentials if needed:

```php
$host = 'localhost';        // Your MySQL host
$dbname = 'lifepilot_db';  // Database name
$username = 'root';         // Your MySQL username
$password = '';             // Your MySQL password (empty for XAMPP default)
```

## Step 2: Create Database in phpMyAdmin

### 2.1 Access phpMyAdmin
1. Start XAMPP
2. Open your web browser
3. Go to `http://localhost/phpmyadmin`

### 2.2 Create Database
1. Click on "Databases" tab
2. Enter database name: `lifepilot_db`
3. Select collation: `utf8mb4_unicode_ci`
4. Click "Create"

## Step 3: Import Database Schema

### 3.1 Using SQL Script
1. Select the `lifepilot_db` database
2. Click on "SQL" tab
3. Copy and paste the contents of `database/lifepilot_mysql_setup.sql`
4. Click "Go" to execute

### 3.2 Manual Table Creation (Alternative)
If you prefer to create tables manually, use the following SQL commands:

#### Users Table
```sql
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    profile_image_url TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### User Preferences Table
```sql
CREATE TABLE user_preferences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    theme VARCHAR(20) DEFAULT 'light',
    language VARCHAR(10) DEFAULT 'ar',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### Tasks Table
```sql
CREATE TABLE tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    status ENUM('pending', 'in_progress', 'completed') DEFAULT 'pending',
    due_date DATETIME,
    completed_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_due_date (user_id, due_date),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### Quotes Table
```sql
CREATE TABLE quotes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    text TEXT NOT NULL,
    author VARCHAR(255),
    language VARCHAR(10) DEFAULT 'ar',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_language (language)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## Step 4: Insert Sample Data

### 4.1 Default Admin User
The setup script creates a default admin user:
- **Email:** <EMAIL>
- **Password:** admin123

**⚠️ Important:** Change this password after first login!

### 4.2 Sample Quotes
The script also inserts motivational quotes in Arabic, English, and French for the AI assistant feature.

## Step 5: Test the Installation

### 5.1 Run Connection Test
1. Open your web browser
2. Navigate to `http://localhost/LifePilot/test-mysql-connection.php`
3. Verify all tests pass

### 5.2 Access the Application
1. Go to `http://localhost/LifePilot/`
2. You should be redirected to the login page
3. Try logging in with the default admin credentials

## Step 6: Application Features

### 6.1 User Management
- User registration and login
- Profile management
- Theme and language preferences

### 6.2 Task Management
- Create, edit, and delete tasks
- Set priorities (low, medium, high)
- Set due dates
- Mark tasks as completed
- Quick task creation

### 6.3 Dashboard Features
- Today's tasks overview
- Task statistics
- Completion rate tracking
- Weekly progress charts

### 6.4 Additional Features
- Multi-language support (Arabic, English, French)
- Dark/Light theme toggle
- Mini calendar widget
- AI assistant with motivational quotes
- Responsive design for mobile devices

## Troubleshooting

### Common Issues

#### Database Connection Error
- Verify MySQL is running in XAMPP
- Check database credentials in `config/db.php`
- Ensure database `lifepilot_db` exists

#### Tables Not Found
- Run the SQL setup script again
- Check if tables were created in phpMyAdmin

#### Login Issues
- Verify the admin user was created
- Check password: `admin123`
- Clear browser cache and cookies

#### Permission Errors
- Ensure proper file permissions
- Check XAMPP directory permissions

### Getting Help
If you encounter issues:
1. Check the browser console for JavaScript errors
2. Check PHP error logs
3. Verify all files are in the correct directory structure
4. Ensure all dependencies are properly configured

## Security Recommendations

1. **Change Default Password:** Immediately change the admin password
2. **Database Security:** Use strong database passwords in production
3. **File Permissions:** Set appropriate file permissions
4. **HTTPS:** Use HTTPS in production environments
5. **Regular Updates:** Keep your system and dependencies updated

## File Structure
```
LifePilot/
├── api/
├── assets/
├── components/
├── config/
├── database/
├── includes/
├── lang/
├── index.php
├── login.php
├── logout.php
└── test-mysql-connection.php
```

Your LifePilot application is now ready to use with MySQL!

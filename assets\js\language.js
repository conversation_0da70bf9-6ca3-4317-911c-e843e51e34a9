// Language management
function changeLanguage(language) {
    // Update user preferences
    updateUserPreferences('language', language)
        .then(() => {
            // Reload page to apply new language
            window.location.reload();
        })
        .catch(error => {
            console.error('Error changing language:', error);
            showMessage('Failed to change language', 'error');
        });
}

// Initialize calendar
function initializeCalendar() {
    if (!document.getElementById('calendarDays')) return;
    
    renderCalendar(currentDate);
}

// Render calendar for given month
function renderCalendar(date) {
    const year = date.getFullYear();
    const month = date.getMonth();
    
    // Update month display
    const monthNames = [
        translations.january, translations.february, translations.march,
        translations.april, translations.may, translations.june,
        translations.july, translations.august, translations.september,
        translations.october, translations.november, translations.december
    ];
    
    const monthHeader = document.getElementById('currentMonth');
    if (monthHeader) {
        monthHeader.textContent = `${monthNames[month]} ${year}`;
    }
    
    // Calculate calendar days
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();
    
    const calendarDays = document.getElementById('calendarDays');
    if (!calendarDays) return;
    
    calendarDays.innerHTML = '';
    
    // Add empty cells for days before first day of month
    for (let i = 0; i < startingDayOfWeek; i++) {
        const emptyDay = document.createElement('div');
        emptyDay.className = 'calendar-day other-month';
        calendarDays.appendChild(emptyDay);
    }
    
    // Add days of the month
    const today = new Date();
    for (let day = 1; day <= daysInMonth; day++) {
        const dayElement = document.createElement('div');
        dayElement.className = 'calendar-day';
        dayElement.textContent = day;
        
        const currentDate = new Date(year, month, day);
        
        // Highlight today
        if (currentDate.toDateString() === today.toDateString()) {
            dayElement.classList.add('today');
        }
        
        // Add click handler
        dayElement.addEventListener('click', () => {
            selectCalendarDay(year, month, day);
        });
        
        calendarDays.appendChild(dayElement);
    }
}

// Handle calendar day selection
function selectCalendarDay(year, month, day) {
    const selectedDate = new Date(year, month, day);
    const dateString = selectedDate.toISOString().split('T')[0];
    
    // Update quick task date
    const quickTaskDate = document.getElementById('quickTaskDate');
    if (quickTaskDate) {
        quickTaskDate.value = dateString;
    }
    
    // Update modal task date
    const taskDueDate = document.getElementById('taskDueDate');
    if (taskDueDate) {
        taskDueDate.value = dateString;
    }
    
    // Show message
    showMessage(`Selected date: ${formatDate(selectedDate)}`, 'info', 2000);
}

// Navigate to previous month
function previousMonth() {
    currentDate.setMonth(currentDate.getMonth() - 1);
    renderCalendar(currentDate);
}

// Navigate to next month
function nextMonth() {
    currentDate.setMonth(currentDate.getMonth() + 1);
    renderCalendar(currentDate);
}

// Go to today
function goToToday() {
    currentDate = new Date();
    renderCalendar(currentDate);
}
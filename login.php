<?php
session_start();
require_once 'config/db.php';
require_once 'includes/auth.php';

// If user is already logged in, redirect to dashboard
if (isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit();
}

$error = '';
$success = '';

// Handle login form submission
if ($_POST['action'] === 'login' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($email) || empty($password)) {
        $error = 'Please fill in all fields.';
    } else {
        $user = loginUser($email, $password);
        if ($user) {
            $_SESSION['user_id'] = $user['id'];
            header('Location: index.php');
            exit();
        } else {
            $error = 'Invalid email or password.';
        }
    }
}

// Handle registration form submission
if ($_POST['action'] === 'register' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $firstName = trim($_POST['first_name'] ?? '');
    $lastName = trim($_POST['last_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    
    if (empty($firstName) || empty($lastName) || empty($email) || empty($password)) {
        $error = 'Please fill in all fields.';
    } elseif ($password !== $confirmPassword) {
        $error = 'Passwords do not match.';
    } elseif (strlen($password) < 6) {
        $error = 'Password must be at least 6 characters long.';
    } else {
        $result = registerUser($firstName, $lastName, $email, $password);
        if ($result === true) {
            $success = 'Registration successful! You can now log in.';
        } else {
            $error = $result; // Error message from registerUser function
        }
    }
}

// Get language preference from URL or default to Arabic
$language = $_GET['lang'] ?? 'ar';
if (!in_array($language, ['ar', 'en', 'fr'])) {
    $language = 'ar';
}

// Include language file
require_once "lang/{$language}.php";
?>

<!DOCTYPE html>
<html lang="<?php echo $language; ?>" dir="<?php echo $language === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $lang['login']; ?> - <?php echo $lang['appTitle']; ?></title>
    <meta name="description" content="<?php echo $lang['appDescription']; ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a'
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800 min-h-screen">
    <!-- Language Selector -->
    <div class="fixed top-4 <?php echo $language === 'ar' ? 'right-4' : 'left-4'; ?> z-50">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-2 border dark:border-gray-700">
            <div class="flex gap-2">
                <a href="?lang=ar" class="w-8 h-6 rounded overflow-hidden border-2 <?php echo $language === 'ar' ? 'border-primary-500' : 'border-transparent hover:border-gray-300'; ?>" title="العربية">
                    <img src="https://flagcdn.com/w40/sa.jpg" alt="العربية" class="w-full h-full object-cover">
                </a>
                <a href="?lang=en" class="w-8 h-6 rounded overflow-hidden border-2 <?php echo $language === 'en' ? 'border-primary-500' : 'border-transparent hover:border-gray-300'; ?>" title="English">
                    <img src="https://flagcdn.com/w40/us.jpg" alt="English" class="w-full h-full object-cover">
                </a>
                <a href="?lang=fr" class="w-8 h-6 rounded overflow-hidden border-2 <?php echo $language === 'fr' ? 'border-primary-500' : 'border-transparent hover:border-gray-300'; ?>" title="Français">
                    <img src="https://flagcdn.com/w40/fr.jpg" alt="Français" class="w-full h-full object-cover">
                </a>
            </div>
        </div>
    </div>

    <!-- Theme Toggle -->
    <div class="fixed top-4 <?php echo $language === 'ar' ? 'left-4' : 'right-4'; ?> z-50">
        <button onclick="toggleTheme()" class="bg-white dark:bg-gray-800 p-3 rounded-full shadow-lg border dark:border-gray-700 hover:shadow-xl transition-all duration-300">
            <svg class="h-5 w-5 text-gray-700 dark:hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
            </svg>
            <svg class="h-5 w-5 text-yellow-400 hidden dark:block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
            </svg>
        </button>
    </div>

    <div class="container mx-auto px-4 py-16 flex items-center justify-center min-h-screen">
        <div class="w-full max-w-md">
            <!-- Logo and Title -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    <?php echo $lang['appTitle']; ?>
                </h1>
                <p class="text-gray-600 dark:text-gray-300">
                    <?php echo $lang['appDescription']; ?>
                </p>
            </div>

            <!-- Auth Forms Container -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
                <!-- Error/Success Messages -->
                <?php if ($error): ?>
                    <div class="mb-4 p-4 bg-red-100 dark:bg-red-900 border border-red-300 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg">
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="mb-4 p-4 bg-green-100 dark:bg-green-900 border border-green-300 dark:border-green-700 text-green-700 dark:text-green-300 rounded-lg">
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>

                <!-- Tab Navigation -->
                <div class="flex mb-6 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                    <button onclick="showLoginForm()" id="loginTab" class="flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors bg-white dark:bg-gray-600 text-primary-600 dark:text-primary-400 shadow-sm">
                        <?php echo $lang['login']; ?>
                    </button>
                    <button onclick="showRegisterForm()" id="registerTab" class="flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
                        <?php echo $lang['register']; ?>
                    </button>
                </div>

                <!-- Login Form -->
                <form id="loginForm" method="POST" class="space-y-4">
                    <input type="hidden" name="action" value="login">
                    
                    <div>
                        <label for="login_email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <?php echo $lang['email']; ?>
                        </label>
                        <input type="email" id="login_email" name="email" required
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>

                    <div>
                        <label for="login_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <?php echo $lang['password']; ?>
                        </label>
                        <input type="password" id="login_password" name="password" required
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>

                    <button type="submit" class="w-full bg-primary-500 hover:bg-primary-600 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                        <?php echo $lang['login']; ?>
                    </button>
                </form>

                <!-- Register Form -->
                <form id="registerForm" method="POST" class="space-y-4 hidden">
                    <input type="hidden" name="action" value="register">
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="first_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                <?php echo $lang['firstName']; ?>
                            </label>
                            <input type="text" id="first_name" name="first_name" required
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        </div>
                        <div>
                            <label for="last_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                <?php echo $lang['lastName']; ?>
                            </label>
                            <input type="text" id="last_name" name="last_name" required
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        </div>
                    </div>

                    <div>
                        <label for="register_email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <?php echo $lang['email']; ?>
                        </label>
                        <input type="email" id="register_email" name="email" required
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>

                    <div>
                        <label for="register_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <?php echo $lang['password']; ?>
                        </label>
                        <input type="password" id="register_password" name="password" required
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>

                    <div>
                        <label for="confirm_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <?php echo $lang['confirmPassword']; ?>
                        </label>
                        <input type="password" id="confirm_password" name="confirm_password" required
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>

                    <button type="submit" class="w-full bg-primary-500 hover:bg-primary-600 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                        <?php echo $lang['register']; ?>
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Theme handling
        function toggleTheme() {
            document.documentElement.classList.toggle('dark');
            const isDark = document.documentElement.classList.contains('dark');
            localStorage.setItem('theme', isDark ? 'dark' : 'light');
        }

        // Load saved theme
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            document.documentElement.classList.add('dark');
        }

        // Form switching
        function showLoginForm() {
            document.getElementById('loginForm').classList.remove('hidden');
            document.getElementById('registerForm').classList.add('hidden');
            document.getElementById('loginTab').classList.add('bg-white', 'dark:bg-gray-600', 'text-primary-600', 'dark:text-primary-400', 'shadow-sm');
            document.getElementById('loginTab').classList.remove('text-gray-600', 'dark:text-gray-300');
            document.getElementById('registerTab').classList.remove('bg-white', 'dark:bg-gray-600', 'text-primary-600', 'dark:text-primary-400', 'shadow-sm');
            document.getElementById('registerTab').classList.add('text-gray-600', 'dark:text-gray-300');
        }

        function showRegisterForm() {
            document.getElementById('registerForm').classList.remove('hidden');
            document.getElementById('loginForm').classList.add('hidden');
            document.getElementById('registerTab').classList.add('bg-white', 'dark:bg-gray-600', 'text-primary-600', 'dark:text-primary-400', 'shadow-sm');
            document.getElementById('registerTab').classList.remove('text-gray-600', 'dark:text-gray-300');
            document.getElementById('loginTab').classList.remove('bg-white', 'dark:bg-gray-600', 'text-primary-600', 'dark:text-primary-400', 'shadow-sm');
            document.getElementById('loginTab').classList.add('text-gray-600', 'dark:text-gray-300');
        }
    </script>
</body>
</html>
<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

session_start();
require_once '../config/db.php';
require_once '../includes/auth.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

try {
    // Get user preferences for language
    $preferences = getUserPreferences($_SESSION['user_id']);
    $language = $preferences['language'] ?? 'ar';
    
    $pdo = getDB();
    
    // Get a random quote in the user's preferred language
    $stmt = $pdo->prepare("SELECT text, author FROM quotes WHERE language = ? ORDER BY RAND() LIMIT 1");
    $stmt->execute([$language]);
    $quote = $stmt->fetch();
    
    if ($quote) {
        echo json_encode([
            'success' => true,
            'quote' => [
                'text' => $quote['text'],
                'author' => $quote['author']
            ]
        ]);
    } else {
        // Fallback to English if no quotes in preferred language
        $stmt = $pdo->prepare("SELECT text, author FROM quotes WHERE language = 'en' ORDER BY RAND() LIMIT 1");
        $stmt->execute();
        $quote = $stmt->fetch();
        
        if ($quote) {
            echo json_encode([
                'success' => true,
                'quote' => [
                    'text' => $quote['text'],
                    'author' => $quote['author']
                ]
            ]);
        } else {
            // Ultimate fallback
            echo json_encode([
                'success' => true,
                'quote' => [
                    'text' => 'The way to get started is to quit talking and begin doing.',
                    'author' => 'Walt Disney'
                ]
            ]);
        }
    }
    
} catch (PDOException $e) {
    error_log("Quote API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database error',
        'quote' => [
            'text' => 'Success is not final, failure is not fatal: it is the courage to continue that counts.',
            'author' => 'Winston Churchill'
        ]
    ]);
}
?>
